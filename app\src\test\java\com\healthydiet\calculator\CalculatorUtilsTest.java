package com.healthydiet.calculator;

import static org.junit.Assert.*;

import com.healthydiet.model.User;

import org.junit.Test;

/**
 * 计算器工具类单元测试
 * 验证整合计算功能的准确性
 */
public class CalculatorUtilsTest {
    
    private static final double DELTA = 0.1; // 允许的误差范围
    
    @Test
    public void testCompleteHealthDataCalculation() {
        // 创建测试用户
        User user = new User("测试用户", "male", 30, 175.0, 75.0, 70.0);
        String activityLevel = CalorieDeficitCalculator.ACTIVITY_MODERATE;
        int timeFrame = 60; // 60天
        
        // 计算完整健康数据
        CalculatorUtils.HealthCalculationResult result = 
            CalculatorUtils.calculateCompleteHealthData(user, activityLevel, timeFrame);
        
        // 验证结果有效性
        assertTrue("计算结果应该有效", result.isValid());
        assertNotNull("用户对象不应该为空", result.getUser());
        assertEquals("用户应该匹配", user, result.getUser());
        
        // 验证BMR计算
        double expectedBMR = BMRCalculator.calculateBMR(user.getGender(), user.getAge(), user.getWeight(), user.getHeight());
        assertEquals("BMR应该正确", expectedBMR, result.getBmr(), DELTA);
        
        // 验证TDEE计算
        double expectedTDEE = CalorieDeficitCalculator.calculateTotalCalories(expectedBMR, activityLevel);
        assertEquals("TDEE应该正确", expectedTDEE, result.getTdee(), DELTA);
        
        // 验证活动水平
        assertEquals("活动水平应该正确", activityLevel, result.getActivityLevel());
        assertNotNull("活动描述不应该为空", result.getActivityDescription());
        
        // 验证热量缺口
        double expectedDeficit = CalorieDeficitCalculator.calculateDeficit(user.getWeight(), user.getTargetWeight(), timeFrame);
        assertEquals("热量缺口应该正确", expectedDeficit, result.getCalorieDeficit(), DELTA);
        
        // 验证建议摄入
        double expectedIntake = CalorieDeficitCalculator.calculateRecommendedIntake(expectedTDEE, expectedDeficit);
        assertEquals("建议摄入应该正确", expectedIntake, result.getRecommendedIntake(), DELTA);
        
        // 验证其他数据
        assertNotNull("BMR范围不应该为空", result.getBmrRange());
        assertEquals("BMR范围应该有两个值", 2, result.getBmrRange().length);
        
        assertNotNull("理想体重范围不应该为空", result.getIdealWeightRange());
        assertEquals("理想体重范围应该有两个值", 2, result.getIdealWeightRange().length);
        
        assertNotNull("安全性评估不应该为空", result.getSafetyAssessment());
        assertNotNull("建议不应该为空", result.getSuggestion());
        assertTrue("建议应该包含有用信息", result.getSuggestion().length() > 10);
    }
    
    @Test
    public void testQuickCalculation() {
        // 测试快速计算
        String gender = "female";
        int age = 25;
        double weight = 60.0;
        double height = 165.0;
        String activityLevel = CalorieDeficitCalculator.ACTIVITY_LIGHT;
        
        CalculatorUtils.QuickCalculationResult result = 
            CalculatorUtils.quickCalculate(gender, age, weight, height, activityLevel);
        
        // 验证结果
        assertTrue("快速计算结果应该有效", result.isValid());
        
        double expectedBMR = BMRCalculator.calculateBMR(gender, age, weight, height);
        assertEquals("BMR应该正确", expectedBMR, result.getBmr(), DELTA);
        
        double expectedTDEE = CalorieDeficitCalculator.calculateTotalCalories(expectedBMR, activityLevel);
        assertEquals("TDEE应该正确", expectedTDEE, result.getTdee(), DELTA);
        
        assertEquals("活动水平应该正确", activityLevel, result.getActivityLevel());
        assertNotNull("活动描述不应该为空", result.getActivityDescription());
    }
    
    @Test
    public void testWeightLossDeficitCalculation() {
        // 测试减重热量缺口计算
        double currentWeight = 80.0;
        double targetWeight = 75.0;
        int weeks = 10;
        
        double deficit = CalculatorUtils.calculateWeightLossDeficit(currentWeight, targetWeight, weeks);
        
        // 手动验证：5kg在10周(70天)内，每日缺口 = 5 * 7700 / 70 = 550
        double expectedDeficit = 5.0 * 7700.0 / (weeks * 7);
        assertEquals("减重热量缺口应该正确", expectedDeficit, deficit, DELTA);
        
        // 测试无需减重的情况
        double noDeficit = CalculatorUtils.calculateWeightLossDeficit(70.0, 75.0, 10);
        assertEquals("无需减重时缺口应该为0", 0.0, noDeficit, DELTA);
    }
    
    @Test
    public void testWeightGainSurplusCalculation() {
        // 测试增重热量盈余计算
        double currentWeight = 60.0;
        double targetWeight = 65.0;
        int weeks = 10;
        
        double surplus = CalculatorUtils.calculateWeightGainSurplus(currentWeight, targetWeight, weeks);
        
        // 手动验证：5kg在10周(70天)内，每日盈余 = -5 * 7700 / 70 = -550（负值表示需要盈余）
        double expectedSurplus = -5.0 * 7700.0 / (weeks * 7);
        assertEquals("增重热量盈余应该正确", expectedSurplus, surplus, DELTA);
        
        // 测试无需增重的情况
        double noSurplus = CalculatorUtils.calculateWeightGainSurplus(75.0, 70.0, 10);
        assertEquals("无需增重时盈余应该为0", 0.0, noSurplus, DELTA);
    }
    
    @Test
    public void testDeficitByWeeklyLoss() {
        // 测试根据每周减重量计算缺口
        double weeklyLoss = 0.5; // 每周减重0.5kg
        
        double dailyDeficit = CalculatorUtils.calculateDeficitByWeeklyLoss(weeklyLoss);
        
        // 手动验证：0.5kg/周 * 7700 kcal/kg / 7天 = 550 kcal/day
        double expectedDeficit = weeklyLoss * 7700.0 / 7.0;
        assertEquals("每周减重缺口计算应该正确", expectedDeficit, dailyDeficit, DELTA);
        
        // 测试无效输入
        double zeroDeficit = CalculatorUtils.calculateDeficitByWeeklyLoss(0.0);
        assertEquals("零减重量应该返回0缺口", 0.0, zeroDeficit, DELTA);
        
        double negativeDeficit = CalculatorUtils.calculateDeficitByWeeklyLoss(-0.5);
        assertEquals("负减重量应该返回0缺口", 0.0, negativeDeficit, DELTA);
    }
    
    @Test
    public void testActivityLevelsComparison() {
        // 测试活动水平比较
        double bmr = 1500.0;
        
        CalculatorUtils.ActivityComparison comparison = CalculatorUtils.compareActivityLevels(bmr);
        
        assertTrue("活动水平比较应该有效", comparison.isValid());
        assertEquals("BMR应该正确", bmr, comparison.getBmr(), DELTA);
        
        String[] levels = comparison.getActivityLevels();
        double[] factors = comparison.getActivityFactors();
        double[] tdees = comparison.getTdees();
        String[] descriptions = comparison.getDescriptions();
        
        assertNotNull("活动水平不应该为空", levels);
        assertNotNull("活动系数不应该为空", factors);
        assertNotNull("TDEE数组不应该为空", tdees);
        assertNotNull("描述数组不应该为空", descriptions);
        
        assertEquals("数组长度应该一致", levels.length, factors.length);
        assertEquals("数组长度应该一致", levels.length, tdees.length);
        assertEquals("数组长度应该一致", levels.length, descriptions.length);
        
        // 验证TDEE计算
        for (int i = 0; i < levels.length; i++) {
            double expectedTDEE = bmr * factors[i];
            assertEquals("TDEE计算应该正确", expectedTDEE, tdees[i], DELTA);
        }
        
        // 验证TDEE递增
        for (int i = 1; i < tdees.length; i++) {
            assertTrue("TDEE应该随活动水平递增", tdees[i] > tdees[i-1]);
        }
    }
    
    @Test
    public void testInvalidInputs() {
        // 测试无效输入
        
        // 空用户对象
        CalculatorUtils.HealthCalculationResult nullUserResult = 
            CalculatorUtils.calculateCompleteHealthData(null, CalorieDeficitCalculator.ACTIVITY_MODERATE, 60);
        assertFalse("空用户应该返回无效结果", nullUserResult.isValid());
        assertTrue("错误信息应该包含用户为空", nullUserResult.getErrorMessage().contains("用户对象为空"));
        
        // 无效用户数据
        User invalidUser = new User("", "invalid", -1, 0.0, 0.0, 0.0);
        CalculatorUtils.HealthCalculationResult invalidUserResult = 
            CalculatorUtils.calculateCompleteHealthData(invalidUser, CalorieDeficitCalculator.ACTIVITY_MODERATE, 60);
        assertFalse("无效用户数据应该返回无效结果", invalidUserResult.isValid());
        assertTrue("错误信息应该包含数据无效", invalidUserResult.getErrorMessage().contains("用户数据无效"));
        
        // 快速计算无效输入
        CalculatorUtils.QuickCalculationResult invalidQuickResult = 
            CalculatorUtils.quickCalculate("invalid", -1, 0.0, 0.0, "invalid");
        assertFalse("无效快速计算输入应该返回无效结果", invalidQuickResult.isValid());
        
        // 无效BMR的活动水平比较
        CalculatorUtils.ActivityComparison invalidComparison = CalculatorUtils.compareActivityLevels(-100.0);
        assertFalse("无效BMR应该返回无效比较结果", invalidComparison.isValid());
    }
    
    @Test
    public void testSuggestionGeneration() {
        // 测试建议生成
        
        // 减重用户
        User weightLossUser = new User("减重用户", "male", 30, 175.0, 80.0, 70.0);
        CalculatorUtils.HealthCalculationResult weightLossResult = 
            CalculatorUtils.calculateCompleteHealthData(weightLossUser, CalorieDeficitCalculator.ACTIVITY_SEDENTARY, 90);
        
        assertTrue("减重结果应该有效", weightLossResult.isValid());
        String weightLossSuggestion = weightLossResult.getSuggestion();
        assertNotNull("减重建议不应该为空", weightLossSuggestion);
        assertTrue("减重建议应该包含相关内容", 
                  weightLossSuggestion.contains("减重") || weightLossSuggestion.contains("饮食控制"));
        
        // 增重用户
        User weightGainUser = new User("增重用户", "female", 25, 165.0, 50.0, 55.0);
        CalculatorUtils.HealthCalculationResult weightGainResult = 
            CalculatorUtils.calculateCompleteHealthData(weightGainUser, CalorieDeficitCalculator.ACTIVITY_LIGHT, 60);
        
        assertTrue("增重结果应该有效", weightGainResult.isValid());
        String weightGainSuggestion = weightGainResult.getSuggestion();
        assertNotNull("增重建议不应该为空", weightGainSuggestion);
        assertTrue("增重建议应该包含相关内容", 
                  weightGainSuggestion.contains("增重") || weightGainSuggestion.contains("营养摄入"));
        
        // 维持体重用户
        User maintainUser = new User("维持用户", "male", 35, 180.0, 75.0, 75.0);
        CalculatorUtils.HealthCalculationResult maintainResult = 
            CalculatorUtils.calculateCompleteHealthData(maintainUser, CalorieDeficitCalculator.ACTIVITY_MODERATE, 30);
        
        assertTrue("维持结果应该有效", maintainResult.isValid());
        String maintainSuggestion = maintainResult.getSuggestion();
        assertNotNull("维持建议不应该为空", maintainSuggestion);
        assertTrue("维持建议应该包含相关内容", 
                  maintainSuggestion.contains("维持") || maintainSuggestion.contains("接近目标"));
    }
    
    @Test
    public void testResultConsistency() {
        // 测试结果一致性
        User user = new User("一致性测试", "female", 28, 168.0, 65.0, 60.0);
        String activity = CalorieDeficitCalculator.ACTIVITY_MODERATE;
        int timeFrame = 84; // 12周
        
        // 多次计算应该得到相同结果
        CalculatorUtils.HealthCalculationResult result1 = 
            CalculatorUtils.calculateCompleteHealthData(user, activity, timeFrame);
        CalculatorUtils.HealthCalculationResult result2 = 
            CalculatorUtils.calculateCompleteHealthData(user, activity, timeFrame);
        
        assertTrue("两次计算都应该有效", result1.isValid() && result2.isValid());
        assertEquals("BMR应该一致", result1.getBmr(), result2.getBmr(), 0.001);
        assertEquals("TDEE应该一致", result1.getTdee(), result2.getTdee(), 0.001);
        assertEquals("热量缺口应该一致", result1.getCalorieDeficit(), result2.getCalorieDeficit(), 0.001);
        assertEquals("建议摄入应该一致", result1.getRecommendedIntake(), result2.getRecommendedIntake(), 0.001);
    }
    
    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // 极端减重目标
        User extremeUser = new User("极端用户", "male", 30, 175.0, 100.0, 60.0);
        CalculatorUtils.HealthCalculationResult extremeResult = 
            CalculatorUtils.calculateCompleteHealthData(extremeUser, CalorieDeficitCalculator.ACTIVITY_MODERATE, 30);
        
        assertTrue("极端减重计算应该有效", extremeResult.isValid());
        assertNotNull("安全性评估不应该为空", extremeResult.getSafetyAssessment());
        assertFalse("极端减重应该被评为不安全", extremeResult.getSafetyAssessment().isSafe());
        
        // 老年用户
        User elderlyUser = new User("老年用户", "female", 70, 160.0, 60.0, 58.0);
        CalculatorUtils.HealthCalculationResult elderlyResult = 
            CalculatorUtils.calculateCompleteHealthData(elderlyUser, CalorieDeficitCalculator.ACTIVITY_LIGHT, 120);
        
        assertTrue("老年用户计算应该有效", elderlyResult.isValid());
        assertTrue("老年用户BMR应该较低", elderlyResult.getBmr() < 1400);
        
        // 年轻高活动用户
        User youngActiveUser = new User("年轻活跃用户", "male", 20, 185.0, 80.0, 75.0);
        CalculatorUtils.HealthCalculationResult youngActiveResult = 
            CalculatorUtils.calculateCompleteHealthData(youngActiveUser, CalorieDeficitCalculator.ACTIVITY_EXTREME, 60);
        
        assertTrue("年轻活跃用户计算应该有效", youngActiveResult.isValid());
        assertTrue("年轻活跃用户TDEE应该较高", youngActiveResult.getTdee() > 3000);
    }
}
