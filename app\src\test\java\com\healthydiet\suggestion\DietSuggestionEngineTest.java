package com.healthydiet.suggestion;

import static org.junit.Assert.*;

import com.healthydiet.model.User;

import org.junit.Test;

/**
 * 饮食建议引擎单元测试
 * 验证饮食建议生成的准确性和完整性
 */
public class DietSuggestionEngineTest {
    
    @Test
    public void testGenerateSuggestionForWeightLoss() {
        // 测试减重饮食建议生成
        User user = new User("减重用户", "female", 30, 165.0, 70.0, 60.0);
        double recommendedCalories = 1500.0;
        String activityLevel = "moderate";
        
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, recommendedCalories, activityLevel);
        
        assertTrue("减重建议应该有效", suggestion.isValid());
        assertEquals("用户应该匹配", user, suggestion.getUser());
        assertEquals("目标应该是减重", DietSuggestionEngine.GOAL_WEIGHT_LOSS, suggestion.getGoal());
        assertEquals("总热量应该正确", recommendedCalories, suggestion.getTotalCalories(), 0.1);
        assertEquals("活动水平应该正确", activityLevel, suggestion.getActivityLevel());
        
        assertNotNull("营养素分配不应该为空", suggestion.getMacronutrients());
        assertTrue("营养素分配应该有效", suggestion.getMacronutrients().isValid());
        
        assertNotNull("餐次建议不应该为空", suggestion.getMealSuggestions());
        assertTrue("应该有餐次建议", suggestion.getMealSuggestions().size() >= 3);
        
        assertNotNull("建议文本不应该为空", suggestion.getAdviceText());
        assertTrue("建议文本应该包含减重相关内容", 
                  suggestion.getAdviceText().contains("减重") || suggestion.getAdviceText().contains("控制"));
        
        assertNotNull("健康提示不应该为空", suggestion.getHealthTips());
        assertTrue("应该有健康提示", suggestion.getHealthTips().size() > 0);
    }
    
    @Test
    public void testGenerateSuggestionForWeightGain() {
        // 测试增重饮食建议生成
        User user = new User("增重用户", "male", 25, 180.0, 60.0, 70.0);
        double recommendedCalories = 2500.0;
        String activityLevel = "light";
        
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, recommendedCalories, activityLevel);
        
        assertTrue("增重建议应该有效", suggestion.isValid());
        assertEquals("目标应该是增重", DietSuggestionEngine.GOAL_WEIGHT_GAIN, suggestion.getGoal());
        
        // 验证营养素分配适合增重
        assertNotNull("营养素分配不应该为空", suggestion.getMacronutrients());
        assertTrue("增重期脂肪比例应该适中", suggestion.getMacronutrients().getFatPercentage() >= 0.25);
        
        // 验证建议文本包含增重相关内容
        assertTrue("建议文本应该包含增重相关内容", 
                  suggestion.getAdviceText().contains("增重") || suggestion.getAdviceText().contains("增加"));
    }
    
    @Test
    public void testGenerateSuggestionForMaintenance() {
        // 测试维持体重饮食建议生成
        User user = new User("维持用户", "male", 35, 175.0, 75.0, 75.0);
        double recommendedCalories = 2200.0;
        String activityLevel = "moderate";
        
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, recommendedCalories, activityLevel);
        
        assertTrue("维持建议应该有效", suggestion.isValid());
        assertEquals("目标应该是维持", DietSuggestionEngine.GOAL_MAINTAIN, suggestion.getGoal());
        
        // 验证建议文本包含维持相关内容
        assertTrue("建议文本应该包含维持相关内容", 
                  suggestion.getAdviceText().contains("维持") || suggestion.getAdviceText().contains("保持"));
    }
    
    @Test
    public void testMealSuggestionsStructure() {
        // 测试餐次建议结构
        User user = new User("测试用户", "female", 28, 168.0, 65.0, 60.0);
        double recommendedCalories = 1800.0;
        String activityLevel = "moderate";
        
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, recommendedCalories, activityLevel);
        
        assertTrue("建议应该有效", suggestion.isValid());
        
        // 验证餐次建议
        assertNotNull("餐次建议不应该为空", suggestion.getMealSuggestions());
        assertTrue("应该至少有3个餐次", suggestion.getMealSuggestions().size() >= 3);
        
        // 验证每个餐次的结构
        for (DietSuggestionEngine.MealSuggestion meal : suggestion.getMealSuggestions()) {
            assertNotNull("餐次类型不应该为空", meal.getMealType());
            assertNotNull("餐次名称不应该为空", meal.getMealName());
            assertTrue("目标热量应该大于0", meal.getTargetCalories() > 0);
            assertTrue("目标蛋白质应该大于0", meal.getTargetProtein() > 0);
            assertTrue("目标碳水应该大于0", meal.getTargetCarbs() > 0);
            assertTrue("目标脂肪应该大于0", meal.getTargetFat() > 0);
            assertNotNull("食物推荐不应该为空", meal.getFoodRecommendations());
            assertNotNull("描述不应该为空", meal.getDescription());
        }
        
        // 验证餐次热量分配合理
        double totalMealCalories = 0;
        for (DietSuggestionEngine.MealSuggestion meal : suggestion.getMealSuggestions()) {
            totalMealCalories += meal.getTargetCalories();
        }
        assertEquals("餐次总热量应该接近推荐热量", recommendedCalories, totalMealCalories, 50.0);
    }
    
    @Test
    public void testHighCalorieSnackAddition() {
        // 测试高热量需求时的加餐添加
        User user = new User("高热量用户", "male", 25, 185.0, 80.0, 75.0);
        double highCalories = 2500.0; // 高热量需求
        String activityLevel = "heavy";
        
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, highCalories, activityLevel);
        
        assertTrue("高热量建议应该有效", suggestion.isValid());
        
        // 验证是否包含加餐
        boolean hasSnack = false;
        for (DietSuggestionEngine.MealSuggestion meal : suggestion.getMealSuggestions()) {
            if ("snack".equals(meal.getMealType())) {
                hasSnack = true;
                break;
            }
        }
        assertTrue("高热量需求应该包含加餐", hasSnack);
    }
    
    @Test
    public void testActivityLevelImpactOnAdvice() {
        // 测试活动水平对建议的影响
        User user = new User("活跃用户", "male", 30, 175.0, 75.0, 70.0);
        double calories = 2000.0;
        
        // 久坐建议
        DietSuggestionEngine.DietSuggestion sedentaryAdvice = 
            DietSuggestionEngine.generateSuggestion(user, calories, "sedentary");
        assertTrue("久坐建议应该有效", sedentaryAdvice.isValid());
        assertTrue("久坐建议应该包含活动建议", 
                  sedentaryAdvice.getAdviceText().contains("活动") || 
                  sedentaryAdvice.getHealthTips().toString().contains("活动"));
        
        // 高强度运动建议
        DietSuggestionEngine.DietSuggestion extremeAdvice = 
            DietSuggestionEngine.generateSuggestion(user, calories, "extreme");
        assertTrue("高强度建议应该有效", extremeAdvice.isValid());
        assertTrue("高强度建议应该包含恢复建议", 
                  extremeAdvice.getAdviceText().contains("恢复") || 
                  extremeAdvice.getHealthTips().toString().contains("恢复"));
    }
    
    @Test
    public void testAgeImpactOnSuggestions() {
        // 测试年龄对建议的影响
        
        // 年轻用户
        User youngUser = new User("年轻用户", "female", 25, 165.0, 60.0, 55.0);
        DietSuggestionEngine.DietSuggestion youngAdvice = 
            DietSuggestionEngine.generateSuggestion(youngUser, 1800.0, "moderate");
        assertTrue("年轻用户建议应该有效", youngAdvice.isValid());
        
        // 老年用户
        User elderlyUser = new User("老年用户", "female", 70, 165.0, 60.0, 55.0);
        DietSuggestionEngine.DietSuggestion elderlyAdvice = 
            DietSuggestionEngine.generateSuggestion(elderlyUser, 1600.0, "light");
        assertTrue("老年用户建议应该有效", elderlyAdvice.isValid());
        
        // 老年用户应该有更高的蛋白质比例
        assertTrue("老年用户蛋白质比例应该更高", 
                  elderlyAdvice.getMacronutrients().getProteinPercentage() >= 
                  youngAdvice.getMacronutrients().getProteinPercentage());
        
        // 老年用户应该有特殊的健康提示
        assertTrue("老年用户应该有钙质相关提示", 
                  elderlyAdvice.getHealthTips().toString().contains("钙") || 
                  elderlyAdvice.getHealthTips().toString().contains("维生素D"));
    }
    
    @Test
    public void testSimpleSuggestionGeneration() {
        // 测试简化建议生成
        User user = new User("简化测试用户", "male", 30, 175.0, 75.0, 70.0);
        
        // 需要减少摄入
        String deficitSuggestion = DietSuggestionEngine.generateSimpleSuggestion(300.0, user);
        assertNotNull("缺口建议不应该为空", deficitSuggestion);
        assertTrue("缺口建议应该包含减少相关内容", 
                  deficitSuggestion.contains("减少") || deficitSuggestion.contains("300"));
        
        // 需要增加摄入
        String surplusSuggestion = DietSuggestionEngine.generateSimpleSuggestion(-200.0, user);
        assertNotNull("盈余建议不应该为空", surplusSuggestion);
        assertTrue("盈余建议应该包含增加相关内容", 
                  surplusSuggestion.contains("增加") || surplusSuggestion.contains("200"));
        
        // 平衡状态
        String balanceSuggestion = DietSuggestionEngine.generateSimpleSuggestion(50.0, user);
        assertNotNull("平衡建议不应该为空", balanceSuggestion);
        assertTrue("平衡建议应该包含维持相关内容", 
                  balanceSuggestion.contains("平衡") || balanceSuggestion.contains("维持"));
    }
    
    @Test
    public void testInvalidInputs() {
        // 测试无效输入
        
        // 空用户
        DietSuggestionEngine.DietSuggestion nullUserSuggestion = 
            DietSuggestionEngine.generateSuggestion(null, 2000.0, "moderate");
        assertFalse("空用户应该返回无效建议", nullUserSuggestion.isValid());
        assertTrue("错误信息应该包含用户无效", nullUserSuggestion.getErrorMessage().contains("用户"));
        
        // 无效用户数据
        User invalidUser = new User("", "invalid", -1, 0.0, 0.0, 0.0);
        DietSuggestionEngine.DietSuggestion invalidUserSuggestion = 
            DietSuggestionEngine.generateSuggestion(invalidUser, 2000.0, "moderate");
        assertFalse("无效用户数据应该返回无效建议", invalidUserSuggestion.isValid());
        
        // 无效热量
        User validUser = new User("有效用户", "male", 30, 175.0, 75.0, 70.0);
        DietSuggestionEngine.DietSuggestion invalidCaloriesSuggestion = 
            DietSuggestionEngine.generateSuggestion(validUser, -100.0, "moderate");
        assertFalse("无效热量应该返回无效建议", invalidCaloriesSuggestion.isValid());
        assertTrue("错误信息应该包含热量无效", invalidCaloriesSuggestion.getErrorMessage().contains("热量"));
        
        // 简化建议的无效输入
        String nullUserSimple = DietSuggestionEngine.generateSimpleSuggestion(300.0, null);
        assertTrue("空用户简化建议应该包含错误信息", nullUserSimple.contains("无效"));
    }
    
    @Test
    public void testMealTypeConsistency() {
        // 测试餐次类型一致性
        User user = new User("一致性测试用户", "female", 32, 170.0, 68.0, 63.0);
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, 1900.0, "moderate");
        
        assertTrue("建议应该有效", suggestion.isValid());
        
        // 验证餐次类型和名称的一致性
        for (DietSuggestionEngine.MealSuggestion meal : suggestion.getMealSuggestions()) {
            String mealType = meal.getMealType();
            String mealName = meal.getMealName();
            
            if ("breakfast".equals(mealType)) {
                assertEquals("早餐类型和名称应该一致", "早餐", mealName);
            } else if ("lunch".equals(mealType)) {
                assertEquals("午餐类型和名称应该一致", "午餐", mealName);
            } else if ("dinner".equals(mealType)) {
                assertEquals("晚餐类型和名称应该一致", "晚餐", mealName);
            } else if ("snack".equals(mealType)) {
                assertEquals("加餐类型和名称应该一致", "加餐", mealName);
            }
        }
    }
    
    @Test
    public void testNutritionDistributionAccuracy() {
        // 测试营养分配准确性
        User user = new User("营养测试用户", "male", 28, 178.0, 72.0, 68.0);
        double totalCalories = 2100.0;
        DietSuggestionEngine.DietSuggestion suggestion = 
            DietSuggestionEngine.generateSuggestion(user, totalCalories, "moderate");
        
        assertTrue("建议应该有效", suggestion.isValid());
        
        // 验证营养素分配的合理性
        assertNotNull("营养素分配不应该为空", suggestion.getMacronutrients());
        
        double proteinPercentage = suggestion.getMacronutrients().getProteinPercentage();
        double carbPercentage = suggestion.getMacronutrients().getCarbPercentage();
        double fatPercentage = suggestion.getMacronutrients().getFatPercentage();
        
        // 验证比例在合理范围内
        assertTrue("蛋白质比例应该在合理范围内", proteinPercentage >= 0.15 && proteinPercentage <= 0.35);
        assertTrue("碳水比例应该在合理范围内", carbPercentage >= 0.40 && carbPercentage <= 0.65);
        assertTrue("脂肪比例应该在合理范围内", fatPercentage >= 0.15 && fatPercentage <= 0.35);
        
        // 验证比例总和为100%
        double totalPercentage = proteinPercentage + carbPercentage + fatPercentage;
        assertEquals("营养素比例总和应该为100%", 1.0, totalPercentage, 0.01);
    }
    
    @Test
    public void testSuggestionConsistency() {
        // 测试建议一致性
        User user = new User("一致性用户", "female", 26, 162.0, 58.0, 55.0);
        double calories = 1700.0;
        String activity = "light";
        
        // 多次生成应该得到一致的结果
        DietSuggestionEngine.DietSuggestion suggestion1 = 
            DietSuggestionEngine.generateSuggestion(user, calories, activity);
        DietSuggestionEngine.DietSuggestion suggestion2 = 
            DietSuggestionEngine.generateSuggestion(user, calories, activity);
        
        assertTrue("两次建议都应该有效", suggestion1.isValid() && suggestion2.isValid());
        assertEquals("目标应该一致", suggestion1.getGoal(), suggestion2.getGoal());
        assertEquals("总热量应该一致", suggestion1.getTotalCalories(), suggestion2.getTotalCalories(), 0.001);
        assertEquals("餐次数量应该一致", 
                    suggestion1.getMealSuggestions().size(), suggestion2.getMealSuggestions().size());
        
        // 营养素分配应该一致
        assertEquals("蛋白质比例应该一致", 
                    suggestion1.getMacronutrients().getProteinPercentage(),
                    suggestion2.getMacronutrients().getProteinPercentage(), 0.001);
    }
}
