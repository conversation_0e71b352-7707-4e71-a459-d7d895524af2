package com.healthydiet.model;

import java.util.Date;

/**
 * 用户数据模型类
 * 存储用户的基本信息，用于BMR计算和个性化建议
 */
public class User {
    private long id;
    private String name;
    private String gender; // "male" 或 "female"
    private int age;
    private double height; // 身高，单位：cm
    private double weight; // 体重，单位：kg
    private double targetWeight; // 目标体重，单位：kg
    private Date createdAt;
    private Date updatedAt;

    // 构造函数
    public User() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    public User(String name, String gender, int age, double height, double weight, double targetWeight) {
        this();
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.height = height;
        this.weight = weight;
        this.targetWeight = targetWeight;
    }

    // Getter和Setter方法
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updatedAt = new Date();
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
        this.updatedAt = new Date();
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
        this.updatedAt = new Date();
    }

    public double getHeight() {
        return height;
    }

    public void setHeight(double height) {
        this.height = height;
        this.updatedAt = new Date();
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
        this.updatedAt = new Date();
    }

    public double getTargetWeight() {
        return targetWeight;
    }

    public void setTargetWeight(double targetWeight) {
        this.targetWeight = targetWeight;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 工具方法
    public boolean isMale() {
        return "male".equalsIgnoreCase(gender);
    }

    public boolean isFemale() {
        return "female".equalsIgnoreCase(gender);
    }

    /**
     * 验证用户数据的有效性
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() &&
               (isMale() || isFemale()) &&
               age > 0 && age < 150 &&
               height > 50 && height < 300 &&
               weight > 20 && weight < 500 &&
               targetWeight > 20 && targetWeight < 500;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", height=" + height +
                ", weight=" + weight +
                ", targetWeight=" + targetWeight +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
