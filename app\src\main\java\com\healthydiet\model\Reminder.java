package com.healthydiet.model;

import java.util.Date;

/**
 * 提醒数据模型类
 * 存储用户设置的各种提醒信息
 */
public class Reminder {
    // 提醒类型常量
    public static final String TYPE_WEIGHT = "weight"; // 体重记录提醒
    public static final String TYPE_MEAL = "meal"; // 饮食记录提醒
    public static final String TYPE_EXERCISE = "exercise"; // 运动提醒

    private long id;
    private long userId; // 关联的用户ID
    private String type; // 提醒类型
    private String time; // 提醒时间 (HH:mm格式)
    private boolean enabled; // 是否启用
    private String title; // 提醒标题
    private String message; // 提醒内容
    private String repeatDays; // 重复日期 (1234567表示周一到周日)
    private Date createdAt;
    private Date updatedAt;

    // 构造函数
    public Reminder() {
        this.enabled = true;
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.repeatDays = "1234567"; // 默认每天重复
    }

    public Reminder(long userId, String type, String time, String title, String message) {
        this();
        this.userId = userId;
        this.type = type;
        this.time = time;
        this.title = title;
        this.message = message;
    }

    // Getter和Setter方法
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
        this.updatedAt = new Date();
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
        this.updatedAt = new Date();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        this.updatedAt = new Date();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = new Date();
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
        this.updatedAt = new Date();
    }

    public String getRepeatDays() {
        return repeatDays;
    }

    public void setRepeatDays(String repeatDays) {
        this.repeatDays = repeatDays;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 工具方法
    /**
     * 验证提醒数据的有效性
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return userId > 0 &&
               type != null && (TYPE_WEIGHT.equals(type) || TYPE_MEAL.equals(type) || TYPE_EXERCISE.equals(type)) &&
               time != null && time.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$") &&
               title != null && !title.trim().isEmpty() &&
               message != null && !message.trim().isEmpty() &&
               repeatDays != null && repeatDays.matches("^[1-7]*$");
    }

    /**
     * 获取提醒类型的显示名称
     * @return 类型显示名称
     */
    public String getTypeDisplayName() {
        switch (type) {
            case TYPE_WEIGHT:
                return "体重记录";
            case TYPE_MEAL:
                return "饮食记录";
            case TYPE_EXERCISE:
                return "运动提醒";
            default:
                return "未知类型";
        }
    }

    /**
     * 检查指定日期是否需要提醒
     * @param dayOfWeek 星期几 (1=周一, 7=周日)
     * @return 如果需要提醒返回true
     */
    public boolean shouldRemindOnDay(int dayOfWeek) {
        if (repeatDays == null || dayOfWeek < 1 || dayOfWeek > 7) {
            return false;
        }
        return repeatDays.contains(String.valueOf(dayOfWeek));
    }

    /**
     * 获取小时数
     * @return 小时 (0-23)
     */
    public int getHour() {
        if (time == null || !time.contains(":")) return 0;
        try {
            return Integer.parseInt(time.split(":")[0]);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 获取分钟数
     * @return 分钟 (0-59)
     */
    public int getMinute() {
        if (time == null || !time.contains(":")) return 0;
        try {
            return Integer.parseInt(time.split(":")[1]);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    @Override
    public String toString() {
        return "Reminder{" +
                "id=" + id +
                ", userId=" + userId +
                ", type='" + type + '\'' +
                ", time='" + time + '\'' +
                ", enabled=" + enabled +
                ", title='" + title + '\'' +
                ", message='" + message + '\'' +
                ", repeatDays='" + repeatDays + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
