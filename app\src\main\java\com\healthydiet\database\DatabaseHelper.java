package com.healthydiet.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.healthydiet.model.HistoryRecord;
import com.healthydiet.model.Reminder;
import com.healthydiet.model.User;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 数据库帮助类
 * 管理SQLite数据库的创建、升级和数据操作
 */
public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "DatabaseHelper";
    
    // 数据库信息
    private static final String DATABASE_NAME = "healthy_diet.db";
    private static final int DATABASE_VERSION = 1;
    
    // 日期格式
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    
    // 用户表
    private static final String TABLE_USERS = "users";
    private static final String COLUMN_USER_ID = "id";
    private static final String COLUMN_USER_NAME = "name";
    private static final String COLUMN_USER_GENDER = "gender";
    private static final String COLUMN_USER_AGE = "age";
    private static final String COLUMN_USER_HEIGHT = "height";
    private static final String COLUMN_USER_WEIGHT = "weight";
    private static final String COLUMN_USER_TARGET_WEIGHT = "target_weight";
    private static final String COLUMN_USER_CREATED_AT = "created_at";
    private static final String COLUMN_USER_UPDATED_AT = "updated_at";
    
    // 历史记录表
    private static final String TABLE_HISTORY = "history";
    private static final String COLUMN_HISTORY_ID = "id";
    private static final String COLUMN_HISTORY_USER_ID = "user_id";
    private static final String COLUMN_HISTORY_DATE = "date";
    private static final String COLUMN_HISTORY_WEIGHT = "weight";
    private static final String COLUMN_HISTORY_BMR = "bmr";
    private static final String COLUMN_HISTORY_CALORIE_DEFICIT = "calorie_deficit";
    private static final String COLUMN_HISTORY_SUGGESTION = "suggestion";
    private static final String COLUMN_HISTORY_ACTIVITY_LEVEL = "activity_level";
    private static final String COLUMN_HISTORY_CREATED_AT = "created_at";
    
    // 提醒表
    private static final String TABLE_REMINDERS = "reminders";
    private static final String COLUMN_REMINDER_ID = "id";
    private static final String COLUMN_REMINDER_USER_ID = "user_id";
    private static final String COLUMN_REMINDER_TYPE = "type";
    private static final String COLUMN_REMINDER_TIME = "time";
    private static final String COLUMN_REMINDER_ENABLED = "enabled";
    private static final String COLUMN_REMINDER_TITLE = "title";
    private static final String COLUMN_REMINDER_MESSAGE = "message";
    private static final String COLUMN_REMINDER_REPEAT_DAYS = "repeat_days";
    private static final String COLUMN_REMINDER_CREATED_AT = "created_at";
    private static final String COLUMN_REMINDER_UPDATED_AT = "updated_at";
    
    // 创建表的SQL语句
    private static final String CREATE_TABLE_USERS = "CREATE TABLE " + TABLE_USERS + "("
            + COLUMN_USER_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_USER_NAME + " TEXT NOT NULL,"
            + COLUMN_USER_GENDER + " TEXT NOT NULL CHECK(" + COLUMN_USER_GENDER + " IN ('male', 'female')),"
            + COLUMN_USER_AGE + " INTEGER NOT NULL CHECK(" + COLUMN_USER_AGE + " > 0 AND " + COLUMN_USER_AGE + " < 150),"
            + COLUMN_USER_HEIGHT + " REAL NOT NULL CHECK(" + COLUMN_USER_HEIGHT + " > 50 AND " + COLUMN_USER_HEIGHT + " < 300),"
            + COLUMN_USER_WEIGHT + " REAL NOT NULL CHECK(" + COLUMN_USER_WEIGHT + " > 20 AND " + COLUMN_USER_WEIGHT + " < 500),"
            + COLUMN_USER_TARGET_WEIGHT + " REAL NOT NULL CHECK(" + COLUMN_USER_TARGET_WEIGHT + " > 20 AND " + COLUMN_USER_TARGET_WEIGHT + " < 500),"
            + COLUMN_USER_CREATED_AT + " TEXT NOT NULL,"
            + COLUMN_USER_UPDATED_AT + " TEXT NOT NULL"
            + ")";
    
    private static final String CREATE_TABLE_HISTORY = "CREATE TABLE " + TABLE_HISTORY + "("
            + COLUMN_HISTORY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_HISTORY_USER_ID + " INTEGER NOT NULL,"
            + COLUMN_HISTORY_DATE + " TEXT NOT NULL,"
            + COLUMN_HISTORY_WEIGHT + " REAL NOT NULL CHECK(" + COLUMN_HISTORY_WEIGHT + " > 0),"
            + COLUMN_HISTORY_BMR + " REAL NOT NULL CHECK(" + COLUMN_HISTORY_BMR + " > 0),"
            + COLUMN_HISTORY_CALORIE_DEFICIT + " REAL NOT NULL,"
            + COLUMN_HISTORY_SUGGESTION + " TEXT NOT NULL,"
            + COLUMN_HISTORY_ACTIVITY_LEVEL + " TEXT NOT NULL,"
            + COLUMN_HISTORY_CREATED_AT + " TEXT NOT NULL,"
            + "FOREIGN KEY(" + COLUMN_HISTORY_USER_ID + ") REFERENCES " + TABLE_USERS + "(" + COLUMN_USER_ID + ") ON DELETE CASCADE"
            + ")";
    
    private static final String CREATE_TABLE_REMINDERS = "CREATE TABLE " + TABLE_REMINDERS + "("
            + COLUMN_REMINDER_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_REMINDER_USER_ID + " INTEGER NOT NULL,"
            + COLUMN_REMINDER_TYPE + " TEXT NOT NULL CHECK(" + COLUMN_REMINDER_TYPE + " IN ('weight', 'meal', 'exercise')),"
            + COLUMN_REMINDER_TIME + " TEXT NOT NULL,"
            + COLUMN_REMINDER_ENABLED + " INTEGER NOT NULL DEFAULT 1,"
            + COLUMN_REMINDER_TITLE + " TEXT NOT NULL,"
            + COLUMN_REMINDER_MESSAGE + " TEXT NOT NULL,"
            + COLUMN_REMINDER_REPEAT_DAYS + " TEXT NOT NULL DEFAULT '1234567',"
            + COLUMN_REMINDER_CREATED_AT + " TEXT NOT NULL,"
            + COLUMN_REMINDER_UPDATED_AT + " TEXT NOT NULL,"
            + "FOREIGN KEY(" + COLUMN_REMINDER_USER_ID + ") REFERENCES " + TABLE_USERS + "(" + COLUMN_USER_ID + ") ON DELETE CASCADE"
            + ")";
    
    // 创建索引的SQL语句
    private static final String CREATE_INDEX_HISTORY_USER_DATE = "CREATE INDEX idx_history_user_date ON " + TABLE_HISTORY + "(" + COLUMN_HISTORY_USER_ID + ", " + COLUMN_HISTORY_DATE + ")";
    private static final String CREATE_INDEX_REMINDERS_USER_TYPE = "CREATE INDEX idx_reminders_user_type ON " + TABLE_REMINDERS + "(" + COLUMN_REMINDER_USER_ID + ", " + COLUMN_REMINDER_TYPE + ")";
    
    private static DatabaseHelper instance;
    
    private DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    /**
     * 获取数据库帮助类的单例实例
     * @param context 上下文
     * @return DatabaseHelper实例
     */
    public static synchronized DatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new DatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        try {
            // 创建表
            db.execSQL(CREATE_TABLE_USERS);
            db.execSQL(CREATE_TABLE_HISTORY);
            db.execSQL(CREATE_TABLE_REMINDERS);
            
            // 创建索引
            db.execSQL(CREATE_INDEX_HISTORY_USER_DATE);
            db.execSQL(CREATE_INDEX_REMINDERS_USER_TYPE);
            
            Log.d(TAG, "数据库表创建成功");
        } catch (Exception e) {
            Log.e(TAG, "创建数据库表失败", e);
        }
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "数据库升级从版本 " + oldVersion + " 到 " + newVersion);
        
        // 简单的升级策略：删除旧表，创建新表
        // 在实际应用中，应该保留用户数据并进行迁移
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_REMINDERS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_HISTORY);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_USERS);
        onCreate(db);
    }
    
    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        // 启用外键约束
        db.setForeignKeyConstraintsEnabled(true);
    }
    
    // ==================== 用户相关操作 ====================
    
    /**
     * 插入新用户
     * @param user 用户对象
     * @return 插入的用户ID，失败返回-1
     */
    public long insertUser(User user) {
        if (user == null || !user.isValid()) {
            Log.e(TAG, "用户数据无效");
            return -1;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_USER_NAME, user.getName());
        values.put(COLUMN_USER_GENDER, user.getGender());
        values.put(COLUMN_USER_AGE, user.getAge());
        values.put(COLUMN_USER_HEIGHT, user.getHeight());
        values.put(COLUMN_USER_WEIGHT, user.getWeight());
        values.put(COLUMN_USER_TARGET_WEIGHT, user.getTargetWeight());
        values.put(COLUMN_USER_CREATED_AT, DATE_FORMAT.format(user.getCreatedAt()));
        values.put(COLUMN_USER_UPDATED_AT, DATE_FORMAT.format(user.getUpdatedAt()));
        
        long id = db.insert(TABLE_USERS, null, values);
        db.close();
        
        if (id != -1) {
            user.setId(id);
            Log.d(TAG, "用户插入成功，ID: " + id);
        } else {
            Log.e(TAG, "用户插入失败");
        }
        
        return id;
    }
    
    /**
     * 更新用户信息
     * @param user 用户对象
     * @return 更新成功返回true
     */
    public boolean updateUser(User user) {
        if (user == null || !user.isValid() || user.getId() <= 0) {
            Log.e(TAG, "用户数据无效或ID无效");
            return false;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_USER_NAME, user.getName());
        values.put(COLUMN_USER_GENDER, user.getGender());
        values.put(COLUMN_USER_AGE, user.getAge());
        values.put(COLUMN_USER_HEIGHT, user.getHeight());
        values.put(COLUMN_USER_WEIGHT, user.getWeight());
        values.put(COLUMN_USER_TARGET_WEIGHT, user.getTargetWeight());
        values.put(COLUMN_USER_UPDATED_AT, DATE_FORMAT.format(new Date()));
        
        int rowsAffected = db.update(TABLE_USERS, values, COLUMN_USER_ID + " = ?", 
                                   new String[]{String.valueOf(user.getId())});
        db.close();
        
        boolean success = rowsAffected > 0;
        Log.d(TAG, "用户更新" + (success ? "成功" : "失败") + "，ID: " + user.getId());
        return success;
    }
    
    /**
     * 根据ID获取用户
     * @param userId 用户ID
     * @return 用户对象，不存在返回null
     */
    public User getUser(long userId) {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_USERS, null, COLUMN_USER_ID + " = ?", 
                               new String[]{String.valueOf(userId)}, null, null, null);
        
        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        
        cursor.close();
        db.close();
        return user;
    }
    
    /**
     * 获取第一个用户（通常应用只有一个用户）
     * @return 用户对象，不存在返回null
     */
    public User getFirstUser() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_USERS, null, null, null, null, null, 
                               COLUMN_USER_ID + " ASC", "1");
        
        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        
        cursor.close();
        db.close();
        return user;
    }
    
    /**
     * 删除用户
     * @param userId 用户ID
     * @return 删除成功返回true
     */
    public boolean deleteUser(long userId) {
        SQLiteDatabase db = this.getWritableDatabase();
        int rowsAffected = db.delete(TABLE_USERS, COLUMN_USER_ID + " = ?", 
                                   new String[]{String.valueOf(userId)});
        db.close();
        
        boolean success = rowsAffected > 0;
        Log.d(TAG, "用户删除" + (success ? "成功" : "失败") + "，ID: " + userId);
        return success;
    }
    
    // ==================== 历史记录相关操作 ====================
    
    /**
     * 插入历史记录
     * @param record 历史记录对象
     * @return 插入的记录ID，失败返回-1
     */
    public long insertHistory(HistoryRecord record) {
        if (record == null || !record.isValid()) {
            Log.e(TAG, "历史记录数据无效");
            return -1;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_HISTORY_USER_ID, record.getUserId());
        values.put(COLUMN_HISTORY_DATE, DATE_FORMAT.format(record.getDate()));
        values.put(COLUMN_HISTORY_WEIGHT, record.getWeight());
        values.put(COLUMN_HISTORY_BMR, record.getBmr());
        values.put(COLUMN_HISTORY_CALORIE_DEFICIT, record.getCalorieDeficit());
        values.put(COLUMN_HISTORY_SUGGESTION, record.getSuggestion());
        values.put(COLUMN_HISTORY_ACTIVITY_LEVEL, record.getActivityLevel());
        values.put(COLUMN_HISTORY_CREATED_AT, DATE_FORMAT.format(record.getCreatedAt()));
        
        long id = db.insert(TABLE_HISTORY, null, values);
        db.close();
        
        if (id != -1) {
            record.setId(id);
            Log.d(TAG, "历史记录插入成功，ID: " + id);
        } else {
            Log.e(TAG, "历史记录插入失败");
        }
        
        return id;
    }
    
    /**
     * 获取用户的历史记录列表
     * @param userId 用户ID
     * @param limit 限制数量，0表示不限制
     * @return 历史记录列表
     */
    public List<HistoryRecord> getHistoryList(long userId, int limit) {
        List<HistoryRecord> historyList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String limitStr = limit > 0 ? String.valueOf(limit) : null;
        Cursor cursor = db.query(TABLE_HISTORY, null, COLUMN_HISTORY_USER_ID + " = ?", 
                               new String[]{String.valueOf(userId)}, null, null, 
                               COLUMN_HISTORY_DATE + " DESC", limitStr);
        
        while (cursor.moveToNext()) {
            HistoryRecord record = cursorToHistoryRecord(cursor);
            if (record != null) {
                historyList.add(record);
            }
        }
        
        cursor.close();
        db.close();
        
        Log.d(TAG, "获取到 " + historyList.size() + " 条历史记录");
        return historyList;
    }
    
    /**
     * 获取用户的所有历史记录
     * @param userId 用户ID
     * @return 历史记录列表
     */
    public List<HistoryRecord> getAllHistory(long userId) {
        return getHistoryList(userId, 0);
    }
    
    /**
     * 删除历史记录
     * @param recordId 记录ID
     * @return 删除成功返回true
     */
    public boolean deleteHistory(long recordId) {
        SQLiteDatabase db = this.getWritableDatabase();
        int rowsAffected = db.delete(TABLE_HISTORY, COLUMN_HISTORY_ID + " = ?", 
                                   new String[]{String.valueOf(recordId)});
        db.close();
        
        boolean success = rowsAffected > 0;
        Log.d(TAG, "历史记录删除" + (success ? "成功" : "失败") + "，ID: " + recordId);
        return success;
    }
    
    // ==================== 提醒相关操作 ====================
    
    /**
     * 插入提醒
     * @param reminder 提醒对象
     * @return 插入的提醒ID，失败返回-1
     */
    public long insertReminder(Reminder reminder) {
        if (reminder == null || !reminder.isValid()) {
            Log.e(TAG, "提醒数据无效");
            return -1;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_REMINDER_USER_ID, reminder.getUserId());
        values.put(COLUMN_REMINDER_TYPE, reminder.getType());
        values.put(COLUMN_REMINDER_TIME, reminder.getTime());
        values.put(COLUMN_REMINDER_ENABLED, reminder.isEnabled() ? 1 : 0);
        values.put(COLUMN_REMINDER_TITLE, reminder.getTitle());
        values.put(COLUMN_REMINDER_MESSAGE, reminder.getMessage());
        values.put(COLUMN_REMINDER_REPEAT_DAYS, reminder.getRepeatDays());
        values.put(COLUMN_REMINDER_CREATED_AT, DATE_FORMAT.format(reminder.getCreatedAt()));
        values.put(COLUMN_REMINDER_UPDATED_AT, DATE_FORMAT.format(reminder.getUpdatedAt()));
        
        long id = db.insert(TABLE_REMINDERS, null, values);
        db.close();
        
        if (id != -1) {
            reminder.setId(id);
            Log.d(TAG, "提醒插入成功，ID: " + id);
        } else {
            Log.e(TAG, "提醒插入失败");
        }
        
        return id;
    }
    
    /**
     * 更新提醒
     * @param reminder 提醒对象
     * @return 更新成功返回true
     */
    public boolean updateReminder(Reminder reminder) {
        if (reminder == null || !reminder.isValid() || reminder.getId() <= 0) {
            Log.e(TAG, "提醒数据无效或ID无效");
            return false;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_REMINDER_TYPE, reminder.getType());
        values.put(COLUMN_REMINDER_TIME, reminder.getTime());
        values.put(COLUMN_REMINDER_ENABLED, reminder.isEnabled() ? 1 : 0);
        values.put(COLUMN_REMINDER_TITLE, reminder.getTitle());
        values.put(COLUMN_REMINDER_MESSAGE, reminder.getMessage());
        values.put(COLUMN_REMINDER_REPEAT_DAYS, reminder.getRepeatDays());
        values.put(COLUMN_REMINDER_UPDATED_AT, DATE_FORMAT.format(new Date()));
        
        int rowsAffected = db.update(TABLE_REMINDERS, values, COLUMN_REMINDER_ID + " = ?", 
                                   new String[]{String.valueOf(reminder.getId())});
        db.close();
        
        boolean success = rowsAffected > 0;
        Log.d(TAG, "提醒更新" + (success ? "成功" : "失败") + "，ID: " + reminder.getId());
        return success;
    }
    
    /**
     * 获取用户的提醒列表
     * @param userId 用户ID
     * @return 提醒列表
     */
    public List<Reminder> getReminders(long userId) {
        List<Reminder> reminderList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        Cursor cursor = db.query(TABLE_REMINDERS, null, COLUMN_REMINDER_USER_ID + " = ?", 
                               new String[]{String.valueOf(userId)}, null, null, 
                               COLUMN_REMINDER_TYPE + ", " + COLUMN_REMINDER_TIME);
        
        while (cursor.moveToNext()) {
            Reminder reminder = cursorToReminder(cursor);
            if (reminder != null) {
                reminderList.add(reminder);
            }
        }
        
        cursor.close();
        db.close();
        
        Log.d(TAG, "获取到 " + reminderList.size() + " 条提醒");
        return reminderList;
    }
    
    /**
     * 获取启用的提醒列表
     * @param userId 用户ID
     * @return 启用的提醒列表
     */
    public List<Reminder> getEnabledReminders(long userId) {
        List<Reminder> reminderList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        Cursor cursor = db.query(TABLE_REMINDERS, null, 
                               COLUMN_REMINDER_USER_ID + " = ? AND " + COLUMN_REMINDER_ENABLED + " = 1", 
                               new String[]{String.valueOf(userId)}, null, null, 
                               COLUMN_REMINDER_TYPE + ", " + COLUMN_REMINDER_TIME);
        
        while (cursor.moveToNext()) {
            Reminder reminder = cursorToReminder(cursor);
            if (reminder != null) {
                reminderList.add(reminder);
            }
        }
        
        cursor.close();
        db.close();
        
        Log.d(TAG, "获取到 " + reminderList.size() + " 条启用的提醒");
        return reminderList;
    }
    
    /**
     * 删除提醒
     * @param reminderId 提醒ID
     * @return 删除成功返回true
     */
    public boolean deleteReminder(long reminderId) {
        SQLiteDatabase db = this.getWritableDatabase();
        int rowsAffected = db.delete(TABLE_REMINDERS, COLUMN_REMINDER_ID + " = ?", 
                                   new String[]{String.valueOf(reminderId)});
        db.close();
        
        boolean success = rowsAffected > 0;
        Log.d(TAG, "提醒删除" + (success ? "成功" : "失败") + "，ID: " + reminderId);
        return success;
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 将Cursor转换为User对象
     */
    private User cursorToUser(Cursor cursor) {
        try {
            User user = new User();
            user.setId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_USER_ID)));
            user.setName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_USER_NAME)));
            user.setGender(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_USER_GENDER)));
            user.setAge(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_USER_AGE)));
            user.setHeight(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_USER_HEIGHT)));
            user.setWeight(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_USER_WEIGHT)));
            user.setTargetWeight(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_USER_TARGET_WEIGHT)));
            
            String createdAtStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_USER_CREATED_AT));
            String updatedAtStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_USER_UPDATED_AT));
            
            user.setCreatedAt(DATE_FORMAT.parse(createdAtStr));
            user.setUpdatedAt(DATE_FORMAT.parse(updatedAtStr));
            
            return user;
        } catch (Exception e) {
            Log.e(TAG, "转换User对象失败", e);
            return null;
        }
    }
    
    /**
     * 将Cursor转换为HistoryRecord对象
     */
    private HistoryRecord cursorToHistoryRecord(Cursor cursor) {
        try {
            HistoryRecord record = new HistoryRecord();
            record.setId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_ID)));
            record.setUserId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_USER_ID)));
            record.setWeight(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_WEIGHT)));
            record.setBmr(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_BMR)));
            record.setCalorieDeficit(cursor.getDouble(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_CALORIE_DEFICIT)));
            record.setSuggestion(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_SUGGESTION)));
            record.setActivityLevel(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_ACTIVITY_LEVEL)));
            
            String dateStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_DATE));
            String createdAtStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_HISTORY_CREATED_AT));
            
            record.setDate(DATE_FORMAT.parse(dateStr));
            record.setCreatedAt(DATE_FORMAT.parse(createdAtStr));
            
            return record;
        } catch (Exception e) {
            Log.e(TAG, "转换HistoryRecord对象失败", e);
            return null;
        }
    }
    
    /**
     * 将Cursor转换为Reminder对象
     */
    private Reminder cursorToReminder(Cursor cursor) {
        try {
            Reminder reminder = new Reminder();
            reminder.setId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_ID)));
            reminder.setUserId(cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_USER_ID)));
            reminder.setType(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_TYPE)));
            reminder.setTime(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_TIME)));
            reminder.setEnabled(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_ENABLED)) == 1);
            reminder.setTitle(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_TITLE)));
            reminder.setMessage(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_MESSAGE)));
            reminder.setRepeatDays(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_REPEAT_DAYS)));
            
            String createdAtStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_CREATED_AT));
            String updatedAtStr = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_REMINDER_UPDATED_AT));
            
            reminder.setCreatedAt(DATE_FORMAT.parse(createdAtStr));
            reminder.setUpdatedAt(DATE_FORMAT.parse(updatedAtStr));
            
            return reminder;
        } catch (Exception e) {
            Log.e(TAG, "转换Reminder对象失败", e);
            return null;
        }
    }
    
    /**
     * 获取数据库中的用户总数
     * @return 用户总数
     */
    public int getUserCount() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_USERS, null);
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        db.close();
        return count;
    }
    
    /**
     * 清空所有数据（用于测试或重置）
     */
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_REMINDERS, null, null);
        db.delete(TABLE_HISTORY, null, null);
        db.delete(TABLE_USERS, null, null);
        db.close();
        Log.d(TAG, "所有数据已清空");
    }
}
