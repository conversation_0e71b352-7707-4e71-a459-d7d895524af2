package com.healthydiet.calculator;

import android.util.Log;

import com.healthydiet.utils.ValidationUtils;

/**
 * 营养计算器
 * 计算营养素分配、热量转换和营养需求
 */
public class NutritionCalculator {
    private static final String TAG = "NutritionCalculator";
    
    // 营养素热量常量 (kcal/g)
    public static final double PROTEIN_CALORIES_PER_GRAM = 4.0; // 蛋白质：4卡路里/克
    public static final double CARB_CALORIES_PER_GRAM = 4.0; // 碳水化合物：4卡路里/克
    public static final double FAT_CALORIES_PER_GRAM = 9.0; // 脂肪：9卡路里/克
    public static final double ALCOHOL_CALORIES_PER_GRAM = 7.0; // 酒精：7卡路里/克
    
    // 推荐营养素比例范围
    public static final double MIN_PROTEIN_PERCENTAGE = 0.15; // 最低蛋白质比例 15%
    public static final double MAX_PROTEIN_PERCENTAGE = 0.35; // 最高蛋白质比例 35%
    public static final double OPTIMAL_PROTEIN_PERCENTAGE = 0.25; // 最佳蛋白质比例 25%
    
    public static final double MIN_CARB_PERCENTAGE = 0.40; // 最低碳水比例 40%
    public static final double MAX_CARB_PERCENTAGE = 0.65; // 最高碳水比例 65%
    public static final double OPTIMAL_CARB_PERCENTAGE = 0.50; // 最佳碳水比例 50%
    
    public static final double MIN_FAT_PERCENTAGE = 0.15; // 最低脂肪比例 15%
    public static final double MAX_FAT_PERCENTAGE = 0.35; // 最高脂肪比例 35%
    public static final double OPTIMAL_FAT_PERCENTAGE = 0.25; // 最佳脂肪比例 25%
    
    // 特殊人群营养需求调整
    public static final double ATHLETE_PROTEIN_PERCENTAGE = 0.30; // 运动员蛋白质比例
    public static final double ELDERLY_PROTEIN_PERCENTAGE = 0.28; // 老年人蛋白质比例
    public static final double WEIGHT_LOSS_PROTEIN_PERCENTAGE = 0.30; // 减重期蛋白质比例
    
    // 每公斤体重蛋白质需求 (g/kg)
    public static final double SEDENTARY_PROTEIN_PER_KG = 0.8; // 久坐人群
    public static final double ACTIVE_PROTEIN_PER_KG = 1.2; // 活跃人群
    public static final double ATHLETE_PROTEIN_PER_KG = 1.6; // 运动员
    public static final double ELDERLY_PROTEIN_PER_KG = 1.0; // 老年人
    
    /**
     * 计算营养素分配
     * @param totalCalories 总热量
     * @param proteinPercentage 蛋白质比例 (0-1)
     * @param carbPercentage 碳水化合物比例 (0-1)
     * @param fatPercentage 脂肪比例 (0-1)
     * @return 营养素分配结果
     */
    public static MacronutrientDistribution calculateMacronutrients(double totalCalories, 
                                                                   double proteinPercentage, 
                                                                   double carbPercentage, 
                                                                   double fatPercentage) {
        MacronutrientDistribution distribution = new MacronutrientDistribution();
        
        // 输入验证
        if (!ValidationUtils.isValidCalories(totalCalories)) {
            distribution.setValid(false);
            distribution.setErrorMessage("无效的总热量值: " + totalCalories);
            return distribution;
        }
        
        if (!isValidMacroPercentages(proteinPercentage, carbPercentage, fatPercentage)) {
            distribution.setValid(false);
            distribution.setErrorMessage("营养素比例无效或总和不等于100%");
            return distribution;
        }
        
        try {
            // 计算各营养素热量
            double proteinCalories = totalCalories * proteinPercentage;
            double carbCalories = totalCalories * carbPercentage;
            double fatCalories = totalCalories * fatPercentage;
            
            // 计算各营养素克数
            double proteinGrams = proteinCalories / PROTEIN_CALORIES_PER_GRAM;
            double carbGrams = carbCalories / CARB_CALORIES_PER_GRAM;
            double fatGrams = fatCalories / FAT_CALORIES_PER_GRAM;
            
            // 设置结果
            distribution.setValid(true);
            distribution.setTotalCalories(totalCalories);
            
            distribution.setProteinPercentage(proteinPercentage);
            distribution.setProteinCalories(proteinCalories);
            distribution.setProteinGrams(ValidationUtils.formatDecimal(proteinGrams, 1));
            
            distribution.setCarbPercentage(carbPercentage);
            distribution.setCarbCalories(carbCalories);
            distribution.setCarbGrams(ValidationUtils.formatDecimal(carbGrams, 1));
            
            distribution.setFatPercentage(fatPercentage);
            distribution.setFatCalories(fatCalories);
            distribution.setFatGrams(ValidationUtils.formatDecimal(fatGrams, 1));
            
            Log.d(TAG, String.format("营养素分配计算成功: 总热量=%.0f, 蛋白质=%.1fg, 碳水=%.1fg, 脂肪=%.1fg", 
                  totalCalories, proteinGrams, carbGrams, fatGrams));
                  
        } catch (Exception e) {
            Log.e(TAG, "营养素分配计算过程中发生异常", e);
            distribution.setValid(false);
            distribution.setErrorMessage("计算过程中发生异常: " + e.getMessage());
        }
        
        return distribution;
    }
    
    /**
     * 根据目标和用户特征计算最佳营养素分配
     * @param totalCalories 总热量
     * @param goal 目标 ("weight_loss", "weight_gain", "maintain", "muscle_gain")
     * @param activityLevel 活动水平
     * @param age 年龄
     * @param weight 体重
     * @return 最佳营养素分配
     */
    public static MacronutrientDistribution calculateOptimalMacronutrients(double totalCalories, 
                                                                          String goal, 
                                                                          String activityLevel, 
                                                                          int age, 
                                                                          double weight) {
        // 根据目标和特征调整营养素比例
        double proteinPercentage = calculateOptimalProteinPercentage(goal, activityLevel, age);
        double fatPercentage = calculateOptimalFatPercentage(goal, age);
        double carbPercentage = 1.0 - proteinPercentage - fatPercentage;
        
        // 验证蛋白质需求是否满足最低要求
        double proteinGrams = (totalCalories * proteinPercentage) / PROTEIN_CALORIES_PER_GRAM;
        double minProteinNeeded = calculateMinProteinRequirement(weight, activityLevel, age);
        
        if (proteinGrams < minProteinNeeded) {
            // 调整蛋白质比例以满足最低需求
            proteinPercentage = (minProteinNeeded * PROTEIN_CALORIES_PER_GRAM) / totalCalories;
            fatPercentage = Math.max(MIN_FAT_PERCENTAGE, fatPercentage);
            carbPercentage = 1.0 - proteinPercentage - fatPercentage;
            
            Log.d(TAG, String.format("调整蛋白质比例以满足最低需求: %.1fg -> %.1fg", 
                  proteinGrams, minProteinNeeded));
        }
        
        return calculateMacronutrients(totalCalories, proteinPercentage, carbPercentage, fatPercentage);
    }
    
    /**
     * 计算最佳蛋白质比例
     * @param goal 目标
     * @param activityLevel 活动水平
     * @param age 年龄
     * @return 蛋白质比例
     */
    private static double calculateOptimalProteinPercentage(String goal, String activityLevel, int age) {
        double basePercentage = OPTIMAL_PROTEIN_PERCENTAGE;
        
        // 根据目标调整
        if ("weight_loss".equals(goal) || "muscle_gain".equals(goal)) {
            basePercentage = WEIGHT_LOSS_PROTEIN_PERCENTAGE;
        }
        
        // 根据活动水平调整
        if (CalorieDeficitCalculator.ACTIVITY_HEAVY.equals(activityLevel) || 
            CalorieDeficitCalculator.ACTIVITY_EXTREME.equals(activityLevel)) {
            basePercentage = ATHLETE_PROTEIN_PERCENTAGE;
        }
        
        // 根据年龄调整
        if (age >= 65) {
            basePercentage = Math.max(basePercentage, ELDERLY_PROTEIN_PERCENTAGE);
        }
        
        return Math.min(MAX_PROTEIN_PERCENTAGE, Math.max(MIN_PROTEIN_PERCENTAGE, basePercentage));
    }
    
    /**
     * 计算最佳脂肪比例
     * @param goal 目标
     * @param age 年龄
     * @return 脂肪比例
     */
    private static double calculateOptimalFatPercentage(String goal, int age) {
        double basePercentage = OPTIMAL_FAT_PERCENTAGE;
        
        // 减重期适当降低脂肪比例
        if ("weight_loss".equals(goal)) {
            basePercentage = 0.20;
        }
        
        // 增重期可以适当增加脂肪比例
        if ("weight_gain".equals(goal)) {
            basePercentage = 0.30;
        }
        
        return Math.min(MAX_FAT_PERCENTAGE, Math.max(MIN_FAT_PERCENTAGE, basePercentage));
    }
    
    /**
     * 计算最低蛋白质需求
     * @param weight 体重
     * @param activityLevel 活动水平
     * @param age 年龄
     * @return 最低蛋白质需求 (g)
     */
    public static double calculateMinProteinRequirement(double weight, String activityLevel, int age) {
        double proteinPerKg = SEDENTARY_PROTEIN_PER_KG;
        
        // 根据活动水平调整
        if (CalorieDeficitCalculator.ACTIVITY_LIGHT.equals(activityLevel) || 
            CalorieDeficitCalculator.ACTIVITY_MODERATE.equals(activityLevel)) {
            proteinPerKg = ACTIVE_PROTEIN_PER_KG;
        } else if (CalorieDeficitCalculator.ACTIVITY_HEAVY.equals(activityLevel) || 
                   CalorieDeficitCalculator.ACTIVITY_EXTREME.equals(activityLevel)) {
            proteinPerKg = ATHLETE_PROTEIN_PER_KG;
        }
        
        // 老年人需要更多蛋白质
        if (age >= 65) {
            proteinPerKg = Math.max(proteinPerKg, ELDERLY_PROTEIN_PER_KG);
        }
        
        return weight * proteinPerKg;
    }
    
    /**
     * 验证营养素比例是否有效
     * @param proteinPercentage 蛋白质比例
     * @param carbPercentage 碳水化合物比例
     * @param fatPercentage 脂肪比例
     * @return 是否有效
     */
    public static boolean isValidMacroPercentages(double proteinPercentage, double carbPercentage, double fatPercentage) {
        // 检查范围
        if (proteinPercentage < 0 || proteinPercentage > 1 ||
            carbPercentage < 0 || carbPercentage > 1 ||
            fatPercentage < 0 || fatPercentage > 1) {
            return false;
        }
        
        // 检查总和是否接近100%（允许1%的误差）
        double total = proteinPercentage + carbPercentage + fatPercentage;
        return Math.abs(total - 1.0) <= 0.01;
    }
    
    /**
     * 验证营养素比例是否在推荐范围内
     * @param proteinPercentage 蛋白质比例
     * @param carbPercentage 碳水化合物比例
     * @param fatPercentage 脂肪比例
     * @return 验证结果
     */
    public static NutritionValidationResult validateMacroPercentages(double proteinPercentage, 
                                                                    double carbPercentage, 
                                                                    double fatPercentage) {
        NutritionValidationResult result = new NutritionValidationResult();
        
        if (!isValidMacroPercentages(proteinPercentage, carbPercentage, fatPercentage)) {
            result.setValid(false);
            result.addError("营养素比例无效或总和不等于100%");
            return result;
        }
        
        // 检查蛋白质比例
        if (proteinPercentage < MIN_PROTEIN_PERCENTAGE) {
            result.addWarning("蛋白质比例过低，建议至少" + (MIN_PROTEIN_PERCENTAGE * 100) + "%");
        } else if (proteinPercentage > MAX_PROTEIN_PERCENTAGE) {
            result.addWarning("蛋白质比例过高，建议不超过" + (MAX_PROTEIN_PERCENTAGE * 100) + "%");
        }
        
        // 检查碳水化合物比例
        if (carbPercentage < MIN_CARB_PERCENTAGE) {
            result.addWarning("碳水化合物比例过低，建议至少" + (MIN_CARB_PERCENTAGE * 100) + "%");
        } else if (carbPercentage > MAX_CARB_PERCENTAGE) {
            result.addWarning("碳水化合物比例过高，建议不超过" + (MAX_CARB_PERCENTAGE * 100) + "%");
        }
        
        // 检查脂肪比例
        if (fatPercentage < MIN_FAT_PERCENTAGE) {
            result.addWarning("脂肪比例过低，建议至少" + (MIN_FAT_PERCENTAGE * 100) + "%");
        } else if (fatPercentage > MAX_FAT_PERCENTAGE) {
            result.addWarning("脂肪比例过高，建议不超过" + (MAX_FAT_PERCENTAGE * 100) + "%");
        }
        
        return result;
    }
    
    /**
     * 计算食物的总热量
     * @param proteinGrams 蛋白质克数
     * @param carbGrams 碳水化合物克数
     * @param fatGrams 脂肪克数
     * @return 总热量
     */
    public static double calculateTotalCalories(double proteinGrams, double carbGrams, double fatGrams) {
        return (proteinGrams * PROTEIN_CALORIES_PER_GRAM) + 
               (carbGrams * CARB_CALORIES_PER_GRAM) + 
               (fatGrams * FAT_CALORIES_PER_GRAM);
    }
    
    /**
     * 计算食物的营养素比例
     * @param proteinGrams 蛋白质克数
     * @param carbGrams 碳水化合物克数
     * @param fatGrams 脂肪克数
     * @return 营养素比例
     */
    public static double[] calculateMacroPercentages(double proteinGrams, double carbGrams, double fatGrams) {
        double totalCalories = calculateTotalCalories(proteinGrams, carbGrams, fatGrams);
        
        if (totalCalories <= 0) {
            return new double[]{0, 0, 0};
        }
        
        double proteinPercentage = (proteinGrams * PROTEIN_CALORIES_PER_GRAM) / totalCalories;
        double carbPercentage = (carbGrams * CARB_CALORIES_PER_GRAM) / totalCalories;
        double fatPercentage = (fatGrams * FAT_CALORIES_PER_GRAM) / totalCalories;
        
        return new double[]{
            ValidationUtils.formatDecimal(proteinPercentage, 3),
            ValidationUtils.formatDecimal(carbPercentage, 3),
            ValidationUtils.formatDecimal(fatPercentage, 3)
        };
    }
    
    /**
     * 营养素分配结果类
     */
    public static class MacronutrientDistribution {
        private boolean valid = false;
        private String errorMessage = "";
        private double totalCalories;
        
        private double proteinPercentage;
        private double proteinCalories;
        private double proteinGrams;
        
        private double carbPercentage;
        private double carbCalories;
        private double carbGrams;
        
        private double fatPercentage;
        private double fatCalories;
        private double fatGrams;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public double getTotalCalories() { return totalCalories; }
        public void setTotalCalories(double totalCalories) { this.totalCalories = totalCalories; }
        
        public double getProteinPercentage() { return proteinPercentage; }
        public void setProteinPercentage(double proteinPercentage) { this.proteinPercentage = proteinPercentage; }
        
        public double getProteinCalories() { return proteinCalories; }
        public void setProteinCalories(double proteinCalories) { this.proteinCalories = proteinCalories; }
        
        public double getProteinGrams() { return proteinGrams; }
        public void setProteinGrams(double proteinGrams) { this.proteinGrams = proteinGrams; }
        
        public double getCarbPercentage() { return carbPercentage; }
        public void setCarbPercentage(double carbPercentage) { this.carbPercentage = carbPercentage; }
        
        public double getCarbCalories() { return carbCalories; }
        public void setCarbCalories(double carbCalories) { this.carbCalories = carbCalories; }
        
        public double getCarbGrams() { return carbGrams; }
        public void setCarbGrams(double carbGrams) { this.carbGrams = carbGrams; }
        
        public double getFatPercentage() { return fatPercentage; }
        public void setFatPercentage(double fatPercentage) { this.fatPercentage = fatPercentage; }
        
        public double getFatCalories() { return fatCalories; }
        public void setFatCalories(double fatCalories) { this.fatCalories = fatCalories; }
        
        public double getFatGrams() { return fatGrams; }
        public void setFatGrams(double fatGrams) { this.fatGrams = fatGrams; }
        
        @Override
        public String toString() {
            return String.format("MacroDistribution{valid=%s, calories=%.0f, protein=%.1fg(%.0f%%), carb=%.1fg(%.0f%%), fat=%.1fg(%.0f%%)}", 
                               valid, totalCalories, proteinGrams, proteinPercentage*100, 
                               carbGrams, carbPercentage*100, fatGrams, fatPercentage*100);
        }
    }
    
    /**
     * 营养验证结果类
     */
    public static class NutritionValidationResult {
        private boolean valid = true;
        private StringBuilder errors = new StringBuilder();
        private StringBuilder warnings = new StringBuilder();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public void addError(String error) {
            if (errors.length() > 0) errors.append("; ");
            errors.append(error);
            valid = false;
        }
        
        public void addWarning(String warning) {
            if (warnings.length() > 0) warnings.append("; ");
            warnings.append(warning);
        }
        
        public String getErrors() { return errors.toString(); }
        public String getWarnings() { return warnings.toString(); }
        
        public boolean hasErrors() { return !valid; }
        public boolean hasWarnings() { return warnings.length() > 0; }
    }
}
