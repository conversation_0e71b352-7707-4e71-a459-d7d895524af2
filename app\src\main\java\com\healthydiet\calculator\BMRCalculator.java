package com.healthydiet.calculator;

import android.util.Log;

import com.healthydiet.utils.ValidationUtils;

/**
 * 基础代谢率(BMR)计算器
 * 使用Harris-<PERSON>公式计算基础代谢率
 * 支持男性和女性的不同计算公式
 */
public class BMRCalculator {
    private static final String TAG = "BMRCalculator";
    
    // <PERSON><PERSON><PERSON>公式常量 - 男性
    private static final double MALE_CONSTANT = 66.0;
    private static final double MALE_WEIGHT_FACTOR = 13.7;
    private static final double MALE_HEIGHT_FACTOR = 5.0;
    private static final double MALE_AGE_FACTOR = 6.8;
    
    // <PERSON><PERSON><PERSON>公式常量 - 女性
    private static final double FEMALE_CONSTANT = 655.0;
    private static final double FEMALE_WEIGHT_FACTOR = 9.6;
    private static final double FEMALE_HEIGHT_FACTOR = 1.8;
    private static final double FEMALE_AGE_FACTOR = 4.7;
    
    // Mifflin-St Jeor公式常量（备用公式，更准确）
    private static final double MSJ_MALE_CONSTANT = 5.0;
    private static final double MSJ_FEMALE_CONSTANT = -161.0;
    private static final double MSJ_WEIGHT_FACTOR = 10.0;
    private static final double MSJ_HEIGHT_FACTOR = 6.25;
    private static final double MSJ_AGE_FACTOR = 5.0;
    
    /**
     * 使用Harris-Benedict公式计算BMR
     * @param gender 性别 ("male" 或 "female")
     * @param age 年龄
     * @param weight 体重 (kg)
     * @param height 身高 (cm)
     * @return BMR值 (kcal/day)，计算失败返回-1
     */
    public static double calculateBMR(String gender, int age, double weight, double height) {
        // 输入验证
        ValidationUtils.ValidationResult validation = ValidationUtils.validateUserInfo("", gender, age, height, weight);
        if (validation.hasErrors()) {
            Log.e(TAG, "BMR计算输入验证失败: " + validation.getErrors());
            return -1;
        }
        
        double bmr;
        
        try {
            if (ValidationUtils.GENDER_MALE.equalsIgnoreCase(gender)) {
                // 男性公式: BMR = 66 + (13.7 × weight) + (5 × height) - (6.8 × age)
                bmr = MALE_CONSTANT + 
                      (MALE_WEIGHT_FACTOR * weight) + 
                      (MALE_HEIGHT_FACTOR * height) - 
                      (MALE_AGE_FACTOR * age);
                      
                Log.d(TAG, String.format("男性BMR计算: 66 + (13.7 × %.1f) + (5 × %.1f) - (6.8 × %d) = %.2f", 
                      weight, height, age, bmr));
                      
            } else if (ValidationUtils.GENDER_FEMALE.equalsIgnoreCase(gender)) {
                // 女性公式: BMR = 655 + (9.6 × weight) + (1.8 × height) - (4.7 × age)
                bmr = FEMALE_CONSTANT + 
                      (FEMALE_WEIGHT_FACTOR * weight) + 
                      (FEMALE_HEIGHT_FACTOR * height) - 
                      (FEMALE_AGE_FACTOR * age);
                      
                Log.d(TAG, String.format("女性BMR计算: 655 + (9.6 × %.1f) + (1.8 × %.1f) - (4.7 × %d) = %.2f", 
                      weight, height, age, bmr));
                      
            } else {
                Log.e(TAG, "无效的性别参数: " + gender);
                return -1;
            }
            
            // 验证计算结果
            if (!ValidationUtils.isValidBMR(bmr)) {
                Log.e(TAG, "BMR计算结果超出有效范围: " + bmr);
                return -1;
            }
            
            // 格式化结果到小数点后1位
            bmr = ValidationUtils.formatDecimal(bmr, 1);
            
            Log.d(TAG, String.format("BMR计算成功: 性别=%s, 年龄=%d, 体重=%.1fkg, 身高=%.1fcm, BMR=%.1f kcal/day", 
                  gender, age, weight, height, bmr));
                  
            return bmr;
            
        } catch (Exception e) {
            Log.e(TAG, "BMR计算过程中发生异常", e);
            return -1;
        }
    }
    
    /**
     * 使用Mifflin-St Jeor公式计算BMR（更准确的现代公式）
     * @param gender 性别 ("male" 或 "female")
     * @param age 年龄
     * @param weight 体重 (kg)
     * @param height 身高 (cm)
     * @return BMR值 (kcal/day)，计算失败返回-1
     */
    public static double calculateBMRMifflin(String gender, int age, double weight, double height) {
        // 输入验证
        ValidationUtils.ValidationResult validation = ValidationUtils.validateUserInfo("", gender, age, height, weight);
        if (validation.hasErrors()) {
            Log.e(TAG, "Mifflin BMR计算输入验证失败: " + validation.getErrors());
            return -1;
        }
        
        double bmr;
        
        try {
            // Mifflin-St Jeor公式: BMR = (10 × weight) + (6.25 × height) - (5 × age) + gender_constant
            bmr = (MSJ_WEIGHT_FACTOR * weight) + 
                  (MSJ_HEIGHT_FACTOR * height) - 
                  (MSJ_AGE_FACTOR * age);
            
            if (ValidationUtils.GENDER_MALE.equalsIgnoreCase(gender)) {
                bmr += MSJ_MALE_CONSTANT; // +5 for males
                Log.d(TAG, String.format("男性Mifflin BMR计算: (10 × %.1f) + (6.25 × %.1f) - (5 × %d) + 5 = %.2f", 
                      weight, height, age, bmr));
            } else if (ValidationUtils.GENDER_FEMALE.equalsIgnoreCase(gender)) {
                bmr += MSJ_FEMALE_CONSTANT; // -161 for females
                Log.d(TAG, String.format("女性Mifflin BMR计算: (10 × %.1f) + (6.25 × %.1f) - (5 × %d) - 161 = %.2f", 
                      weight, height, age, bmr));
            } else {
                Log.e(TAG, "无效的性别参数: " + gender);
                return -1;
            }
            
            // 验证计算结果
            if (!ValidationUtils.isValidBMR(bmr)) {
                Log.e(TAG, "Mifflin BMR计算结果超出有效范围: " + bmr);
                return -1;
            }
            
            // 格式化结果到小数点后1位
            bmr = ValidationUtils.formatDecimal(bmr, 1);
            
            Log.d(TAG, String.format("Mifflin BMR计算成功: 性别=%s, 年龄=%d, 体重=%.1fkg, 身高=%.1fcm, BMR=%.1f kcal/day", 
                  gender, age, weight, height, bmr));
                  
            return bmr;
            
        } catch (Exception e) {
            Log.e(TAG, "Mifflin BMR计算过程中发生异常", e);
            return -1;
        }
    }
    
    /**
     * 根据用户对象计算BMR
     * @param user 用户对象
     * @return BMR值 (kcal/day)，计算失败返回-1
     */
    public static double calculateBMR(com.healthydiet.model.User user) {
        if (user == null) {
            Log.e(TAG, "用户对象为空，无法计算BMR");
            return -1;
        }
        
        return calculateBMR(user.getGender(), user.getAge(), user.getWeight(), user.getHeight());
    }
    
    /**
     * 计算BMR范围（考虑个体差异）
     * @param gender 性别
     * @param age 年龄
     * @param weight 体重
     * @param height 身高
     * @return BMR范围 [最小值, 最大值]
     */
    public static double[] calculateBMRRange(String gender, int age, double weight, double height) {
        double baseBMR = calculateBMR(gender, age, weight, height);
        if (baseBMR <= 0) {
            return new double[]{-1, -1};
        }
        
        // 考虑个体差异，BMR可能有±10%的变化
        double variation = 0.10;
        double minBMR = baseBMR * (1 - variation);
        double maxBMR = baseBMR * (1 + variation);
        
        return new double[]{
            ValidationUtils.formatDecimal(minBMR, 1),
            ValidationUtils.formatDecimal(maxBMR, 1)
        };
    }
    
    /**
     * 比较Harris-Benedict和Mifflin-St Jeor两种公式的结果
     * @param gender 性别
     * @param age 年龄
     * @param weight 体重
     * @param height 身高
     * @return 计算结果比较 [Harris-Benedict, Mifflin-St Jeor, 差值]
     */
    public static double[] compareBMRFormulas(String gender, int age, double weight, double height) {
        double harrisBMR = calculateBMR(gender, age, weight, height);
        double mifflinBMR = calculateBMRMifflin(gender, age, weight, height);
        
        if (harrisBMR <= 0 || mifflinBMR <= 0) {
            return new double[]{-1, -1, -1};
        }
        
        double difference = Math.abs(harrisBMR - mifflinBMR);
        
        Log.d(TAG, String.format("BMR公式比较: Harris-Benedict=%.1f, Mifflin-St Jeor=%.1f, 差值=%.1f", 
              harrisBMR, mifflinBMR, difference));
        
        return new double[]{harrisBMR, mifflinBMR, difference};
    }
    
    /**
     * 根据BMR估算理想体重范围
     * @param gender 性别
     * @param age 年龄
     * @param height 身高
     * @param targetBMR 目标BMR
     * @return 理想体重范围 [最小值, 最大值]，计算失败返回[-1, -1]
     */
    public static double[] estimateIdealWeightRange(String gender, int age, double height, double targetBMR) {
        if (!ValidationUtils.isValidGender(gender) || !ValidationUtils.isValidAge(age) || 
            !ValidationUtils.isValidHeight(height) || !ValidationUtils.isValidBMR(targetBMR)) {
            Log.e(TAG, "理想体重计算输入参数无效");
            return new double[]{-1, -1};
        }
        
        try {
            double weightFactor, constant, heightFactor, ageFactor;
            
            if (ValidationUtils.GENDER_MALE.equalsIgnoreCase(gender)) {
                weightFactor = MALE_WEIGHT_FACTOR;
                constant = MALE_CONSTANT;
                heightFactor = MALE_HEIGHT_FACTOR;
                ageFactor = MALE_AGE_FACTOR;
            } else {
                weightFactor = FEMALE_WEIGHT_FACTOR;
                constant = FEMALE_CONSTANT;
                heightFactor = FEMALE_HEIGHT_FACTOR;
                ageFactor = FEMALE_AGE_FACTOR;
            }
            
            // 从BMR公式反推体重: weight = (BMR - constant - height_factor*height + age_factor*age) / weight_factor
            double idealWeight = (targetBMR - constant - (heightFactor * height) + (ageFactor * age)) / weightFactor;
            
            // 考虑±5kg的合理范围
            double minWeight = Math.max(idealWeight - 5, ValidationUtils.MIN_WEIGHT);
            double maxWeight = Math.min(idealWeight + 5, ValidationUtils.MAX_WEIGHT);
            
            if (!ValidationUtils.isValidWeight(minWeight) || !ValidationUtils.isValidWeight(maxWeight)) {
                Log.e(TAG, "计算出的理想体重超出有效范围");
                return new double[]{-1, -1};
            }
            
            return new double[]{
                ValidationUtils.formatDecimal(minWeight, 1),
                ValidationUtils.formatDecimal(maxWeight, 1)
            };
            
        } catch (Exception e) {
            Log.e(TAG, "理想体重计算过程中发生异常", e);
            return new double[]{-1, -1};
        }
    }
    
    /**
     * 获取BMR计算公式的详细说明
     * @param gender 性别
     * @return 公式说明
     */
    public static String getBMRFormulaDescription(String gender) {
        if (ValidationUtils.GENDER_MALE.equalsIgnoreCase(gender)) {
            return "男性Harris-Benedict公式: BMR = 66 + (13.7 × 体重kg) + (5 × 身高cm) - (6.8 × 年龄)";
        } else if (ValidationUtils.GENDER_FEMALE.equalsIgnoreCase(gender)) {
            return "女性Harris-Benedict公式: BMR = 655 + (9.6 × 体重kg) + (1.8 × 身高cm) - (4.7 × 年龄)";
        } else {
            return "无效性别参数";
        }
    }
    
    /**
     * 验证BMR计算结果的合理性
     * @param bmr BMR值
     * @param gender 性别
     * @param age 年龄
     * @param weight 体重
     * @return 验证结果说明
     */
    public static String validateBMRResult(double bmr, String gender, int age, double weight) {
        if (!ValidationUtils.isValidBMR(bmr)) {
            return "BMR值超出正常范围 (" + ValidationUtils.MIN_BMR + "-" + ValidationUtils.MAX_BMR + ")";
        }
        
        // 根据体重估算合理的BMR范围
        double estimatedMinBMR = weight * 20; // 大约每公斤体重20-25卡路里
        double estimatedMaxBMR = weight * 30;
        
        if (bmr < estimatedMinBMR) {
            return "BMR值可能偏低，建议检查输入数据";
        } else if (bmr > estimatedMaxBMR) {
            return "BMR值可能偏高，建议检查输入数据";
        } else {
            return "BMR值在合理范围内";
        }
    }
}
