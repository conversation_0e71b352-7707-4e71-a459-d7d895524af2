<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".CalculateActivity">

    <!-- 主要内容区域 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 输入信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="基本信息"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:layout_marginBottom="16dp" />

                    <!-- 性别选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="性别"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <Spinner
                            android:id="@+id/spinnerGender"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 年龄输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="年龄 (岁)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etAge"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLength="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 身高输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="身高 (cm)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etHeight"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLength="6" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 当前体重输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="当前体重 (kg)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etWeight"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLength="6" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 目标体重输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="目标体重 (kg)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etTargetWeight"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLength="6" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 活动水平选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="活动水平"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <Spinner
                            android:id="@+id/spinnerActivityLevel"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 时间框架输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="达到目标时间 (天)">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etTimeFrame"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLength="4" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 计算按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCalculate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="开始计算"
                        android:textSize="16sp"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 计算结果卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardResults"
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="计算结果"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:layout_marginBottom="16dp" />

                    <!-- BMR结果 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="基础代谢率 (BMR):"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                        <TextView
                            android:id="@+id/tvBMRResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- kcal/day"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body"
                            android:textColor="@color/info_blue"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- TDEE结果 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="总消耗热量 (TDEE):"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                        <TextView
                            android:id="@+id/tvTDEEResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- kcal/day"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body"
                            android:textColor="@color/healthy_green"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- 热量缺口结果 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="热量缺口:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                        <TextView
                            android:id="@+id/tvCalorieDeficitResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- kcal/day"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body"
                            android:textColor="@color/warning_orange"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- 建议摄入结果 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="建议摄入热量:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                        <TextView
                            android:id="@+id/tvRecommendedIntakeResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- kcal/day"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body"
                            android:textColor="@color/primary_color"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- 营养素分配标题 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="营养素分配建议"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Subtitle"
                        android:layout_marginBottom="8dp" />

                    <!-- 蛋白质 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="4dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="蛋白质:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                        <TextView
                            android:id="@+id/tvProteinResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- g"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                    </LinearLayout>

                    <!-- 碳水化合物 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="4dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="碳水化合物:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                        <TextView
                            android:id="@+id/tvCarbResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- g"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                    </LinearLayout>

                    <!-- 脂肪 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="脂肪:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                        <TextView
                            android:id="@+id/tvFatResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- g"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Caption" />

                    </LinearLayout>

                    <!-- 达到目标时间 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="预计达到目标时间:"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                        <TextView
                            android:id="@+id/tvTimeToGoalResult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-- 天"
                            android:textAppearance="@style/TextAppearance.HealthyDiet.Body"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- 安全性评估 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="安全性评估"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Subtitle"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvSafetyAssessment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Caption"
                        android:layout_marginBottom="16dp" />

                    <!-- 保存按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSaveToProfile"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="保存到个人档案"
                        android:textSize="16sp"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
