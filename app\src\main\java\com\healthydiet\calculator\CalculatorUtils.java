package com.healthydiet.calculator;

import android.util.Log;

import com.healthydiet.model.User;
import com.healthydiet.utils.ValidationUtils;

/**
 * 计算器工具类
 * 提供高级的计算接口，整合BMR和热量缺口计算功能
 */
public class CalculatorUtils {
    private static final String TAG = "CalculatorUtils";
    
    /**
     * 计算用户的完整健康数据
     * @param user 用户对象
     * @param activityLevel 活动水平
     * @param timeFrameDays 达到目标的时间框架（天）
     * @return 完整的健康计算结果
     */
    public static HealthCalculationResult calculateCompleteHealthData(User user, String activityLevel, int timeFrameDays) {
        HealthCalculationResult result = new HealthCalculationResult();
        
        if (user == null) {
            result.setValid(false);
            result.setErrorMessage("用户对象为空");
            return result;
        }
        
        if (!user.isValid()) {
            result.setValid(false);
            result.setErrorMessage("用户数据无效: " + getValidationErrors(user));
            return result;
        }
        
        try {
            // 计算BMR
            double bmr = BMRCalculator.calculateBMR(user.getGender(), user.getAge(), user.getWeight(), user.getHeight());
            if (bmr <= 0) {
                result.setValid(false);
                result.setErrorMessage("BMR计算失败");
                return result;
            }
            
            // 计算完整的热量计划
            CalorieDeficitCalculator.CaloriePlan plan = CalorieDeficitCalculator.calculateCompletePlan(
                bmr, activityLevel, user.getWeight(), user.getTargetWeight(), timeFrameDays);
                
            if (!plan.isValid()) {
                result.setValid(false);
                result.setErrorMessage("热量计划计算失败: " + plan.getErrorMessage());
                return result;
            }
            
            // 计算BMR范围
            double[] bmrRange = BMRCalculator.calculateBMRRange(user.getGender(), user.getAge(), user.getWeight(), user.getHeight());
            
            // 计算理想体重范围
            double[] idealWeightRange = BMRCalculator.estimateIdealWeightRange(user.getGender(), user.getAge(), user.getHeight(), bmr);
            
            // 设置结果数据
            result.setValid(true);
            result.setUser(user);
            result.setBmr(bmr);
            result.setBmrRange(bmrRange);
            result.setTdee(plan.getTdee());
            result.setActivityLevel(activityLevel);
            result.setActivityDescription(CalorieDeficitCalculator.getActivityDescription(activityLevel));
            result.setCalorieDeficit(plan.getCalorieDeficit());
            result.setRecommendedIntake(plan.getRecommendedIntake());
            result.setTimeToGoalDays(plan.getTimeToGoalDays());
            result.setSafetyAssessment(plan.getSafetyAssessment());
            result.setIdealWeightRange(idealWeightRange);
            
            // 生成建议
            result.setSuggestion(generateHealthSuggestion(result));
            
            Log.d(TAG, "完整健康数据计算成功: " + result.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "完整健康数据计算过程中发生异常", e);
            result.setValid(false);
            result.setErrorMessage("计算过程中发生异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 快速计算BMR和TDEE
     * @param gender 性别
     * @param age 年龄
     * @param weight 体重
     * @param height 身高
     * @param activityLevel 活动水平
     * @return 快速计算结果
     */
    public static QuickCalculationResult quickCalculate(String gender, int age, double weight, double height, String activityLevel) {
        QuickCalculationResult result = new QuickCalculationResult();
        
        try {
            // 验证输入
            ValidationUtils.ValidationResult validation = ValidationUtils.validateUserInfo("", gender, age, height, weight);
            if (validation.hasErrors()) {
                result.setValid(false);
                result.setErrorMessage("输入验证失败: " + validation.getErrors());
                return result;
            }
            
            // 计算BMR
            double bmr = BMRCalculator.calculateBMR(gender, age, weight, height);
            if (bmr <= 0) {
                result.setValid(false);
                result.setErrorMessage("BMR计算失败");
                return result;
            }
            
            // 计算TDEE
            double tdee = CalorieDeficitCalculator.calculateTotalCalories(bmr, activityLevel);
            if (tdee <= 0) {
                result.setValid(false);
                result.setErrorMessage("TDEE计算失败");
                return result;
            }
            
            result.setValid(true);
            result.setBmr(bmr);
            result.setTdee(tdee);
            result.setActivityLevel(activityLevel);
            result.setActivityDescription(CalorieDeficitCalculator.getActivityDescription(activityLevel));
            
            Log.d(TAG, String.format("快速计算成功: BMR=%.1f, TDEE=%.1f", bmr, tdee));
            
        } catch (Exception e) {
            Log.e(TAG, "快速计算过程中发生异常", e);
            result.setValid(false);
            result.setErrorMessage("计算过程中发生异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 计算减重所需的热量缺口
     * @param currentWeight 当前体重
     * @param targetWeight 目标体重
     * @param weeks 周数
     * @return 每日热量缺口
     */
    public static double calculateWeightLossDeficit(double currentWeight, double targetWeight, int weeks) {
        if (currentWeight <= targetWeight) {
            Log.w(TAG, "当前体重小于等于目标体重，无需减重");
            return 0;
        }
        
        int days = weeks * 7;
        return CalorieDeficitCalculator.calculateDeficit(currentWeight, targetWeight, days);
    }
    
    /**
     * 计算增重所需的热量盈余
     * @param currentWeight 当前体重
     * @param targetWeight 目标体重
     * @param weeks 周数
     * @return 每日热量盈余（负值表示需要增加摄入）
     */
    public static double calculateWeightGainSurplus(double currentWeight, double targetWeight, int weeks) {
        if (currentWeight >= targetWeight) {
            Log.w(TAG, "当前体重大于等于目标体重，无需增重");
            return 0;
        }
        
        int days = weeks * 7;
        return CalorieDeficitCalculator.calculateDeficit(currentWeight, targetWeight, days);
    }
    
    /**
     * 根据目标每周减重量计算热量缺口
     * @param weeklyWeightLoss 每周减重量（kg）
     * @return 每日热量缺口
     */
    public static double calculateDeficitByWeeklyLoss(double weeklyWeightLoss) {
        if (weeklyWeightLoss <= 0) {
            return 0;
        }
        
        double weeklyCalorieDeficit = weeklyWeightLoss * CalorieDeficitCalculator.CALORIES_PER_KG_FAT;
        return weeklyCalorieDeficit / 7.0;
    }
    
    /**
     * 比较不同活动水平下的TDEE
     * @param bmr BMR值
     * @return 不同活动水平的TDEE数组
     */
    public static ActivityComparison compareActivityLevels(double bmr) {
        ActivityComparison comparison = new ActivityComparison();
        
        if (!ValidationUtils.isValidBMR(bmr)) {
            comparison.setValid(false);
            comparison.setErrorMessage("无效的BMR值");
            return comparison;
        }
        
        String[] levels = CalorieDeficitCalculator.getAllActivityLevels();
        double[] factors = CalorieDeficitCalculator.getAllActivityFactors();
        double[] tdees = new double[levels.length];
        String[] descriptions = new String[levels.length];
        
        for (int i = 0; i < levels.length; i++) {
            tdees[i] = bmr * factors[i];
            descriptions[i] = CalorieDeficitCalculator.getActivityDescription(levels[i]);
        }
        
        comparison.setValid(true);
        comparison.setBmr(bmr);
        comparison.setActivityLevels(levels);
        comparison.setActivityFactors(factors);
        comparison.setTdees(tdees);
        comparison.setDescriptions(descriptions);
        
        return comparison;
    }
    
    /**
     * 生成健康建议
     * @param result 健康计算结果
     * @return 健康建议文本
     */
    private static String generateHealthSuggestion(HealthCalculationResult result) {
        StringBuilder suggestion = new StringBuilder();
        
        try {
            User user = result.getUser();
            double weightDiff = user.getWeight() - user.getTargetWeight();
            
            // 基本建议
            if (Math.abs(weightDiff) < 0.5) {
                suggestion.append("您的体重已接近目标，建议维持当前体重。");
            } else if (weightDiff > 0) {
                suggestion.append("建议通过合理的饮食控制和运动来减重。");
            } else {
                suggestion.append("建议通过增加营养摄入和适当运动来健康增重。");
            }
            
            // 热量建议
            suggestion.append(String.format(" 建议每日摄入%.0f卡路里", result.getRecommendedIntake()));
            
            // 活动建议
            if (CalorieDeficitCalculator.ACTIVITY_SEDENTARY.equals(result.getActivityLevel())) {
                suggestion.append("，建议增加日常活动量");
            } else if (CalorieDeficitCalculator.ACTIVITY_EXTREME.equals(result.getActivityLevel())) {
                suggestion.append("，注意运动强度，避免过度训练");
            }
            
            // 安全性建议
            CalorieDeficitCalculator.SafetyAssessment safety = result.getSafetyAssessment();
            if (safety != null && !safety.isSafe()) {
                suggestion.append("。注意：").append(safety.getWarnings());
                if (!safety.getRecommendations().isEmpty()) {
                    suggestion.append(" 建议：").append(safety.getRecommendations());
                }
            }
            
            // 时间建议
            if (result.getTimeToGoalDays() > 0) {
                int weeks = result.getTimeToGoalDays() / 7;
                if (weeks > 0) {
                    suggestion.append(String.format("。预计需要约%d周达到目标体重", weeks));
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "生成健康建议时发生异常", e);
            suggestion.append("建议咨询专业营养师制定个性化方案。");
        }
        
        return suggestion.toString();
    }
    
    /**
     * 获取用户验证错误信息
     * @param user 用户对象
     * @return 错误信息
     */
    private static String getValidationErrors(User user) {
        if (user == null) return "用户对象为空";
        
        ValidationUtils.ValidationResult validation = ValidationUtils.validateUserInfo(
            user.getName(), user.getGender(), user.getAge(), user.getHeight(), user.getWeight());
        return validation.getErrors();
    }
    
    /**
     * 完整健康计算结果类
     */
    public static class HealthCalculationResult {
        private boolean valid = false;
        private String errorMessage = "";
        private User user;
        private double bmr;
        private double[] bmrRange;
        private double tdee;
        private String activityLevel;
        private String activityDescription;
        private double calorieDeficit;
        private double recommendedIntake;
        private int timeToGoalDays;
        private CalorieDeficitCalculator.SafetyAssessment safetyAssessment;
        private double[] idealWeightRange;
        private String suggestion;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public User getUser() { return user; }
        public void setUser(User user) { this.user = user; }
        
        public double getBmr() { return bmr; }
        public void setBmr(double bmr) { this.bmr = bmr; }
        
        public double[] getBmrRange() { return bmrRange; }
        public void setBmrRange(double[] bmrRange) { this.bmrRange = bmrRange; }
        
        public double getTdee() { return tdee; }
        public void setTdee(double tdee) { this.tdee = tdee; }
        
        public String getActivityLevel() { return activityLevel; }
        public void setActivityLevel(String activityLevel) { this.activityLevel = activityLevel; }
        
        public String getActivityDescription() { return activityDescription; }
        public void setActivityDescription(String activityDescription) { this.activityDescription = activityDescription; }
        
        public double getCalorieDeficit() { return calorieDeficit; }
        public void setCalorieDeficit(double calorieDeficit) { this.calorieDeficit = calorieDeficit; }
        
        public double getRecommendedIntake() { return recommendedIntake; }
        public void setRecommendedIntake(double recommendedIntake) { this.recommendedIntake = recommendedIntake; }
        
        public int getTimeToGoalDays() { return timeToGoalDays; }
        public void setTimeToGoalDays(int timeToGoalDays) { this.timeToGoalDays = timeToGoalDays; }
        
        public CalorieDeficitCalculator.SafetyAssessment getSafetyAssessment() { return safetyAssessment; }
        public void setSafetyAssessment(CalorieDeficitCalculator.SafetyAssessment safetyAssessment) { this.safetyAssessment = safetyAssessment; }
        
        public double[] getIdealWeightRange() { return idealWeightRange; }
        public void setIdealWeightRange(double[] idealWeightRange) { this.idealWeightRange = idealWeightRange; }
        
        public String getSuggestion() { return suggestion; }
        public void setSuggestion(String suggestion) { this.suggestion = suggestion; }
        
        @Override
        public String toString() {
            return String.format("HealthResult{valid=%s, BMR=%.1f, TDEE=%.1f, deficit=%.1f, intake=%.1f}", 
                               valid, bmr, tdee, calorieDeficit, recommendedIntake);
        }
    }
    
    /**
     * 快速计算结果类
     */
    public static class QuickCalculationResult {
        private boolean valid = false;
        private String errorMessage = "";
        private double bmr;
        private double tdee;
        private String activityLevel;
        private String activityDescription;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public double getBmr() { return bmr; }
        public void setBmr(double bmr) { this.bmr = bmr; }
        
        public double getTdee() { return tdee; }
        public void setTdee(double tdee) { this.tdee = tdee; }
        
        public String getActivityLevel() { return activityLevel; }
        public void setActivityLevel(String activityLevel) { this.activityLevel = activityLevel; }
        
        public String getActivityDescription() { return activityDescription; }
        public void setActivityDescription(String activityDescription) { this.activityDescription = activityDescription; }
    }
    
    /**
     * 活动水平比较结果类
     */
    public static class ActivityComparison {
        private boolean valid = false;
        private String errorMessage = "";
        private double bmr;
        private String[] activityLevels;
        private double[] activityFactors;
        private double[] tdees;
        private String[] descriptions;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public double getBmr() { return bmr; }
        public void setBmr(double bmr) { this.bmr = bmr; }
        
        public String[] getActivityLevels() { return activityLevels; }
        public void setActivityLevels(String[] activityLevels) { this.activityLevels = activityLevels; }
        
        public double[] getActivityFactors() { return activityFactors; }
        public void setActivityFactors(double[] activityFactors) { this.activityFactors = activityFactors; }
        
        public double[] getTdees() { return tdees; }
        public void setTdees(double[] tdees) { this.tdees = tdees; }
        
        public String[] getDescriptions() { return descriptions; }
        public void setDescriptions(String[] descriptions) { this.descriptions = descriptions; }
    }
}
