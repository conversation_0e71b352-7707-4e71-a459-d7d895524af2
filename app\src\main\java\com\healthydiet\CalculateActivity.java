package com.healthydiet;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.card.MaterialCardView;
import com.healthydiet.calculator.BMRCalculator;
import com.healthydiet.calculator.CalorieDeficitCalculator;
import com.healthydiet.calculator.CalculatorUtils;
import com.healthydiet.calculator.NutritionCalculator;
import com.healthydiet.database.DatabaseManager;
import com.healthydiet.model.User;
import com.healthydiet.utils.ValidationUtils;

/**
 * 计算页面Activity
 * 提供BMR计算、热量缺口计算和营养分配计算功能
 */
public class CalculateActivity extends AppCompatActivity {
    
    private static final String TAG = "CalculateActivity";
    
    // UI组件 - 输入区域
    private EditText etWeight;
    private EditText etHeight;
    private EditText etAge;
    private EditText etTargetWeight;
    private EditText etTimeFrame;
    private Spinner spinnerGender;
    private Spinner spinnerActivityLevel;
    private Button btnCalculate;
    private Button btnSaveToProfile;
    
    // UI组件 - 结果显示区域
    private MaterialCardView cardResults;
    private TextView tvBMRResult;
    private TextView tvTDEEResult;
    private TextView tvCalorieDeficitResult;
    private TextView tvRecommendedIntakeResult;
    private TextView tvProteinResult;
    private TextView tvCarbResult;
    private TextView tvFatResult;
    private TextView tvTimeToGoalResult;
    private TextView tvSafetyAssessment;
    
    // 数据管理
    private DatabaseManager databaseManager;
    private CalculatorUtils.HealthCalculationResult lastCalculationResult;
    
    // 选择器数据
    private String[] genderOptions = {"male", "female"};
    private String[] genderDisplayNames = {"男性", "女性"};
    private String[] activityLevels = {
        CalorieDeficitCalculator.ACTIVITY_SEDENTARY,
        CalorieDeficitCalculator.ACTIVITY_LIGHT,
        CalorieDeficitCalculator.ACTIVITY_MODERATE,
        CalorieDeficitCalculator.ACTIVITY_HEAVY,
        CalorieDeficitCalculator.ACTIVITY_EXTREME
    };
    private String[] activityDisplayNames = {
        "久坐 (很少运动)",
        "轻度活动 (每周1-3次)",
        "中度活动 (每周3-5次)",
        "重度活动 (每周6-7次)",
        "极重活动 (每天2次运动)"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_calculate);
        
        // 设置标题栏
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("健康计算");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // 初始化数据管理器
        databaseManager = DatabaseManager.getInstance(this);
        
        // 初始化界面组件
        initViews();
        
        // 设置选择器
        setupSpinners();
        
        // 设置点击事件
        setupClickListeners();
        
        // 加载用户数据
        loadUserData();
    }
    
    /**
     * 初始化界面组件
     */
    private void initViews() {
        // 输入组件
        etWeight = findViewById(R.id.etWeight);
        etHeight = findViewById(R.id.etHeight);
        etAge = findViewById(R.id.etAge);
        etTargetWeight = findViewById(R.id.etTargetWeight);
        etTimeFrame = findViewById(R.id.etTimeFrame);
        spinnerGender = findViewById(R.id.spinnerGender);
        spinnerActivityLevel = findViewById(R.id.spinnerActivityLevel);
        btnCalculate = findViewById(R.id.btnCalculate);
        btnSaveToProfile = findViewById(R.id.btnSaveToProfile);
        
        // 结果显示组件
        cardResults = findViewById(R.id.cardResults);
        tvBMRResult = findViewById(R.id.tvBMRResult);
        tvTDEEResult = findViewById(R.id.tvTDEEResult);
        tvCalorieDeficitResult = findViewById(R.id.tvCalorieDeficitResult);
        tvRecommendedIntakeResult = findViewById(R.id.tvRecommendedIntakeResult);
        tvProteinResult = findViewById(R.id.tvProteinResult);
        tvCarbResult = findViewById(R.id.tvCarbResult);
        tvFatResult = findViewById(R.id.tvFatResult);
        tvTimeToGoalResult = findViewById(R.id.tvTimeToGoalResult);
        tvSafetyAssessment = findViewById(R.id.tvSafetyAssessment);
        
        // 初始时隐藏结果卡片
        cardResults.setVisibility(View.GONE);
        btnSaveToProfile.setVisibility(View.GONE);
    }
    
    /**
     * 设置选择器
     */
    private void setupSpinners() {
        // 性别选择器
        ArrayAdapter<String> genderAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, genderDisplayNames);
        genderAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerGender.setAdapter(genderAdapter);
        
        // 活动水平选择器
        ArrayAdapter<String> activityAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, activityDisplayNames);
        activityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerActivityLevel.setAdapter(activityAdapter);
        spinnerActivityLevel.setSelection(2); // 默认选择中度活动
    }
    
    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        btnCalculate.setOnClickListener(v -> performCalculation());
        btnSaveToProfile.setOnClickListener(v -> saveToProfile());
    }
    
    /**
     * 加载用户数据
     */
    private void loadUserData() {
        try {
            User currentUser = databaseManager.getCurrentUser();
            if (currentUser != null) {
                // 填充用户数据到输入框
                etWeight.setText(String.valueOf(currentUser.getWeight()));
                etHeight.setText(String.valueOf(currentUser.getHeight()));
                etAge.setText(String.valueOf(currentUser.getAge()));
                etTargetWeight.setText(String.valueOf(currentUser.getTargetWeight()));
                
                // 设置性别选择器
                for (int i = 0; i < genderOptions.length; i++) {
                    if (genderOptions[i].equals(currentUser.getGender())) {
                        spinnerGender.setSelection(i);
                        break;
                    }
                }
                
                // 设置默认时间框架
                etTimeFrame.setText("90");
            }
        } catch (Exception e) {
            Log.e(TAG, "加载用户数据失败", e);
        }
    }
    
    /**
     * 执行计算
     */
    private void performCalculation() {
        try {
            // 获取输入数据
            String weightStr = etWeight.getText().toString().trim();
            String heightStr = etHeight.getText().toString().trim();
            String ageStr = etAge.getText().toString().trim();
            String targetWeightStr = etTargetWeight.getText().toString().trim();
            String timeFrameStr = etTimeFrame.getText().toString().trim();
            
            // 验证输入
            if (TextUtils.isEmpty(weightStr) || TextUtils.isEmpty(heightStr) || 
                TextUtils.isEmpty(ageStr) || TextUtils.isEmpty(targetWeightStr) || 
                TextUtils.isEmpty(timeFrameStr)) {
                showError("请填写所有必填项");
                return;
            }
            
            // 解析数据
            double weight = ValidationUtils.safeParseDouble(weightStr, 0);
            double height = ValidationUtils.safeParseDouble(heightStr, 0);
            int age = ValidationUtils.safeParseInt(ageStr, 0);
            double targetWeight = ValidationUtils.safeParseDouble(targetWeightStr, 0);
            int timeFrame = ValidationUtils.safeParseInt(timeFrameStr, 0);
            
            String gender = genderOptions[spinnerGender.getSelectedItemPosition()];
            String activityLevel = activityLevels[spinnerActivityLevel.getSelectedItemPosition()];
            
            // 验证数据有效性
            ValidationUtils.ValidationResult validation = 
                ValidationUtils.validateUserInfo("临时用户", gender, age, height, weight);
            if (validation.hasErrors()) {
                showError("输入数据无效: " + validation.getErrors());
                return;
            }
            
            if (!ValidationUtils.isValidWeight(targetWeight)) {
                showError("目标体重无效");
                return;
            }
            
            if (!ValidationUtils.isValidTimeFrame(timeFrame)) {
                showError("时间框架无效（1-3650天）");
                return;
            }
            
            // 创建临时用户对象进行计算
            User tempUser = new User("临时用户", gender, age, height, weight, targetWeight);
            
            // 执行完整计算
            lastCalculationResult = CalculatorUtils.calculateCompleteHealthData(tempUser, activityLevel, timeFrame);
            
            if (lastCalculationResult.isValid()) {
                displayResults();
                cardResults.setVisibility(View.VISIBLE);
                btnSaveToProfile.setVisibility(View.VISIBLE);
                Toast.makeText(this, "计算完成", Toast.LENGTH_SHORT).show();
            } else {
                showError("计算失败: " + lastCalculationResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "计算过程中发生异常", e);
            showError("计算失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示计算结果
     */
    private void displayResults() {
        if (lastCalculationResult == null || !lastCalculationResult.isValid()) {
            return;
        }
        
        try {
            // 基础代谢率
            tvBMRResult.setText(String.format("%.0f kcal/day", lastCalculationResult.getBmr()));
            
            // 总消耗热量
            tvTDEEResult.setText(String.format("%.0f kcal/day", lastCalculationResult.getTdee()));
            
            // 热量缺口
            double deficit = lastCalculationResult.getCalorieDeficit();
            if (deficit > 0) {
                tvCalorieDeficitResult.setText(String.format("需要缺口 %.0f kcal/day", deficit));
            } else if (deficit < 0) {
                tvCalorieDeficitResult.setText(String.format("需要盈余 %.0f kcal/day", Math.abs(deficit)));
            } else {
                tvCalorieDeficitResult.setText("维持当前摄入");
            }
            
            // 建议摄入热量
            tvRecommendedIntakeResult.setText(String.format("%.0f kcal/day", lastCalculationResult.getRecommendedIntake()));
            
            // 营养素分配（需要计算）
            displayNutritionDistribution();
            
            // 达到目标时间
            if (lastCalculationResult.getTimeToGoalDays() > 0) {
                int weeks = lastCalculationResult.getTimeToGoalDays() / 7;
                int days = lastCalculationResult.getTimeToGoalDays() % 7;
                if (weeks > 0) {
                    tvTimeToGoalResult.setText(String.format("约 %d 周 %d 天", weeks, days));
                } else {
                    tvTimeToGoalResult.setText(String.format("%d 天", days));
                }
            } else {
                tvTimeToGoalResult.setText("已达到目标");
            }
            
            // 安全性评估
            displaySafetyAssessment();
            
        } catch (Exception e) {
            Log.e(TAG, "显示结果失败", e);
            showError("显示结果失败");
        }
    }
    
    /**
     * 显示营养素分配
     */
    private void displayNutritionDistribution() {
        try {
            String activityLevel = activityLevels[spinnerActivityLevel.getSelectedItemPosition()];
            User user = lastCalculationResult.getUser();
            
            // 计算最佳营养素分配
            NutritionCalculator.MacronutrientDistribution macros = 
                NutritionCalculator.calculateOptimalMacronutrients(
                    lastCalculationResult.getRecommendedIntake(), 
                    determineGoal(user), 
                    activityLevel, 
                    user.getAge(), 
                    user.getWeight());
            
            if (macros.isValid()) {
                tvProteinResult.setText(String.format("%.1f g (%.0f%%)", 
                    macros.getProteinGrams(), macros.getProteinPercentage() * 100));
                tvCarbResult.setText(String.format("%.1f g (%.0f%%)", 
                    macros.getCarbGrams(), macros.getCarbPercentage() * 100));
                tvFatResult.setText(String.format("%.1f g (%.0f%%)", 
                    macros.getFatGrams(), macros.getFatPercentage() * 100));
            } else {
                tvProteinResult.setText("计算失败");
                tvCarbResult.setText("计算失败");
                tvFatResult.setText("计算失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "显示营养分配失败", e);
            tvProteinResult.setText("计算失败");
            tvCarbResult.setText("计算失败");
            tvFatResult.setText("计算失败");
        }
    }
    
    /**
     * 显示安全性评估
     */
    private void displaySafetyAssessment() {
        try {
            CalorieDeficitCalculator.SafetyAssessment safety = lastCalculationResult.getSafetyAssessment();
            if (safety != null) {
                StringBuilder assessment = new StringBuilder();
                assessment.append("安全等级: ").append(safety.getLevel());
                
                if (!safety.getWarnings().isEmpty()) {
                    assessment.append("\n警告: ").append(safety.getWarnings());
                }
                
                if (!safety.getRecommendations().isEmpty()) {
                    assessment.append("\n建议: ").append(safety.getRecommendations());
                }
                
                tvSafetyAssessment.setText(assessment.toString());
                
                // 根据安全等级设置文字颜色
                if (safety.isSafe()) {
                    tvSafetyAssessment.setTextColor(getResources().getColor(R.color.healthy_green, null));
                } else {
                    tvSafetyAssessment.setTextColor(getResources().getColor(R.color.error_red, null));
                }
            } else {
                tvSafetyAssessment.setText("安全性评估不可用");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "显示安全性评估失败", e);
            tvSafetyAssessment.setText("安全性评估失败");
        }
    }
    
    /**
     * 确定用户目标
     */
    private String determineGoal(User user) {
        double weightDiff = user.getWeight() - user.getTargetWeight();
        if (Math.abs(weightDiff) <= 1.0) {
            return "maintain";
        } else if (weightDiff > 1.0) {
            return "weight_loss";
        } else {
            return "weight_gain";
        }
    }
    
    /**
     * 保存到用户档案
     */
    private void saveToProfile() {
        if (lastCalculationResult == null || !lastCalculationResult.isValid()) {
            showError("没有有效的计算结果可保存");
            return;
        }
        
        try {
            User calculatedUser = lastCalculationResult.getUser();
            String activityLevel = activityLevels[spinnerActivityLevel.getSelectedItemPosition()];
            
            // 创建或更新用户信息
            User savedUser = databaseManager.createOrUpdateUser(
                calculatedUser.getName().equals("临时用户") ? "用户" : calculatedUser.getName(),
                calculatedUser.getGender(),
                calculatedUser.getAge(),
                calculatedUser.getHeight(),
                calculatedUser.getWeight(),
                calculatedUser.getTargetWeight()
            );
            
            if (savedUser != null) {
                // 保存计算结果到历史记录
                boolean historySaved = databaseManager.saveCalculationResult(
                    calculatedUser.getWeight(),
                    lastCalculationResult.getBmr(),
                    lastCalculationResult.getCalorieDeficit(),
                    lastCalculationResult.getSuggestion(),
                    activityLevel
                );
                
                if (historySaved) {
                    Toast.makeText(this, "已保存到个人档案", Toast.LENGTH_SHORT).show();
                    btnSaveToProfile.setEnabled(false);
                    btnSaveToProfile.setText("已保存");
                } else {
                    showError("保存历史记录失败");
                }
            } else {
                showError("保存用户信息失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "保存到档案失败", e);
            showError("保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
        Log.e(TAG, message);
    }
    
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
