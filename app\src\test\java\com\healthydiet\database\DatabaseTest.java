package com.healthydiet.database;

import static org.junit.Assert.*;

import android.content.Context;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.healthydiet.model.HistoryRecord;
import com.healthydiet.model.Reminder;
import com.healthydiet.model.User;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

/**
 * 数据库功能测试类
 * 测试数据库的CRUD操作和数据完整性
 */
@RunWith(AndroidJUnit4.class)
public class DatabaseTest {
    
    private DatabaseHelper dbHelper;
    private DatabaseManager dbManager;
    private Context context;
    
    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        dbHelper = DatabaseHelper.getInstance(context);
        dbManager = DatabaseManager.getInstance(context);
        
        // 清空测试数据
        dbHelper.clearAllData();
    }
    
    @After
    public void tearDown() {
        // 清空测试数据
        dbHelper.clearAllData();
    }
    
    @Test
    public void testDatabaseConnection() {
        assertTrue("数据库连接应该正常", dbManager.checkDatabaseConnection());
    }
    
    @Test
    public void testUserCRUD() {
        // 测试创建用户
        User user = new User("张三", "male", 25, 175.0, 70.0, 65.0);
        long userId = dbHelper.insertUser(user);
        assertTrue("用户插入应该成功", userId > 0);
        assertEquals("用户ID应该正确设置", userId, user.getId());
        
        // 测试获取用户
        User retrievedUser = dbHelper.getUser(userId);
        assertNotNull("应该能获取到用户", retrievedUser);
        assertEquals("用户名应该匹配", "张三", retrievedUser.getName());
        assertEquals("性别应该匹配", "male", retrievedUser.getGender());
        assertEquals("年龄应该匹配", 25, retrievedUser.getAge());
        assertEquals("身高应该匹配", 175.0, retrievedUser.getHeight(), 0.01);
        assertEquals("体重应该匹配", 70.0, retrievedUser.getWeight(), 0.01);
        assertEquals("目标体重应该匹配", 65.0, retrievedUser.getTargetWeight(), 0.01);
        
        // 测试更新用户
        retrievedUser.setWeight(68.0);
        retrievedUser.setAge(26);
        boolean updateResult = dbHelper.updateUser(retrievedUser);
        assertTrue("用户更新应该成功", updateResult);
        
        User updatedUser = dbHelper.getUser(userId);
        assertEquals("更新后的体重应该匹配", 68.0, updatedUser.getWeight(), 0.01);
        assertEquals("更新后的年龄应该匹配", 26, updatedUser.getAge());
        
        // 测试删除用户
        boolean deleteResult = dbHelper.deleteUser(userId);
        assertTrue("用户删除应该成功", deleteResult);
        
        User deletedUser = dbHelper.getUser(userId);
        assertNull("删除后应该获取不到用户", deletedUser);
    }
    
    @Test
    public void testUserValidation() {
        // 测试无效用户数据
        User invalidUser1 = new User("", "male", 25, 175.0, 70.0, 65.0); // 空名字
        assertFalse("空名字的用户应该无效", invalidUser1.isValid());
        
        User invalidUser2 = new User("张三", "unknown", 25, 175.0, 70.0, 65.0); // 无效性别
        assertFalse("无效性别的用户应该无效", invalidUser2.isValid());
        
        User invalidUser3 = new User("张三", "male", 0, 175.0, 70.0, 65.0); // 无效年龄
        assertFalse("无效年龄的用户应该无效", invalidUser3.isValid());
        
        User invalidUser4 = new User("张三", "male", 25, 30.0, 70.0, 65.0); // 无效身高
        assertFalse("无效身高的用户应该无效", invalidUser4.isValid());
        
        User invalidUser5 = new User("张三", "male", 25, 175.0, 10.0, 65.0); // 无效体重
        assertFalse("无效体重的用户应该无效", invalidUser5.isValid());
        
        // 测试有效用户数据
        User validUser = new User("张三", "female", 25, 165.0, 55.0, 50.0);
        assertTrue("有效用户应该通过验证", validUser.isValid());
    }
    
    @Test
    public void testHistoryRecordCRUD() {
        // 先创建用户
        User user = new User("李四", "female", 30, 160.0, 60.0, 55.0);
        long userId = dbHelper.insertUser(user);
        
        // 测试创建历史记录
        HistoryRecord record = new HistoryRecord(userId, 60.0, 1350.0, -300.0, 
                                               "建议减少200卡路里摄入", "moderate");
        long recordId = dbHelper.insertHistory(record);
        assertTrue("历史记录插入应该成功", recordId > 0);
        
        // 测试获取历史记录
        List<HistoryRecord> historyList = dbHelper.getHistoryList(userId, 10);
        assertNotNull("应该能获取到历史记录列表", historyList);
        assertEquals("历史记录数量应该正确", 1, historyList.size());
        
        HistoryRecord retrievedRecord = historyList.get(0);
        assertEquals("用户ID应该匹配", userId, retrievedRecord.getUserId());
        assertEquals("体重应该匹配", 60.0, retrievedRecord.getWeight(), 0.01);
        assertEquals("BMR应该匹配", 1350.0, retrievedRecord.getBmr(), 0.01);
        assertEquals("热量缺口应该匹配", -300.0, retrievedRecord.getCalorieDeficit(), 0.01);
        assertEquals("建议应该匹配", "建议减少200卡路里摄入", retrievedRecord.getSuggestion());
        assertEquals("运动强度应该匹配", "moderate", retrievedRecord.getActivityLevel());
        
        // 测试删除历史记录
        boolean deleteResult = dbHelper.deleteHistory(recordId);
        assertTrue("历史记录删除应该成功", deleteResult);
        
        List<HistoryRecord> emptyList = dbHelper.getHistoryList(userId, 10);
        assertEquals("删除后历史记录应该为空", 0, emptyList.size());
    }
    
    @Test
    public void testReminderCRUD() {
        // 先创建用户
        User user = new User("王五", "male", 35, 180.0, 80.0, 75.0);
        long userId = dbHelper.insertUser(user);
        
        // 测试创建提醒
        Reminder reminder = new Reminder(userId, Reminder.TYPE_WEIGHT, "08:00", 
                                       "体重记录提醒", "该记录今天的体重了！");
        long reminderId = dbHelper.insertReminder(reminder);
        assertTrue("提醒插入应该成功", reminderId > 0);
        
        // 测试获取提醒
        List<Reminder> reminderList = dbHelper.getReminders(userId);
        assertNotNull("应该能获取到提醒列表", reminderList);
        assertEquals("提醒数量应该正确", 1, reminderList.size());
        
        Reminder retrievedReminder = reminderList.get(0);
        assertEquals("用户ID应该匹配", userId, retrievedReminder.getUserId());
        assertEquals("提醒类型应该匹配", Reminder.TYPE_WEIGHT, retrievedReminder.getType());
        assertEquals("提醒时间应该匹配", "08:00", retrievedReminder.getTime());
        assertEquals("提醒标题应该匹配", "体重记录提醒", retrievedReminder.getTitle());
        assertEquals("提醒内容应该匹配", "该记录今天的体重了！", retrievedReminder.getMessage());
        assertTrue("提醒应该默认启用", retrievedReminder.isEnabled());
        
        // 测试更新提醒
        retrievedReminder.setTime("09:00");
        retrievedReminder.setEnabled(false);
        boolean updateResult = dbHelper.updateReminder(retrievedReminder);
        assertTrue("提醒更新应该成功", updateResult);
        
        Reminder updatedReminder = dbHelper.getReminders(userId).get(0);
        assertEquals("更新后的时间应该匹配", "09:00", updatedReminder.getTime());
        assertFalse("更新后的启用状态应该匹配", updatedReminder.isEnabled());
        
        // 测试删除提醒
        boolean deleteResult = dbHelper.deleteReminder(reminderId);
        assertTrue("提醒删除应该成功", deleteResult);
        
        List<Reminder> emptyList = dbHelper.getReminders(userId);
        assertEquals("删除后提醒应该为空", 0, emptyList.size());
    }
    
    @Test
    public void testReminderValidation() {
        // 测试无效提醒数据
        Reminder invalidReminder1 = new Reminder(1, "invalid_type", "08:00", "标题", "内容");
        assertFalse("无效类型的提醒应该无效", invalidReminder1.isValid());
        
        Reminder invalidReminder2 = new Reminder(1, Reminder.TYPE_WEIGHT, "25:00", "标题", "内容");
        assertFalse("无效时间的提醒应该无效", invalidReminder2.isValid());
        
        Reminder invalidReminder3 = new Reminder(1, Reminder.TYPE_WEIGHT, "08:00", "", "内容");
        assertFalse("空标题的提醒应该无效", invalidReminder3.isValid());
        
        Reminder invalidReminder4 = new Reminder(1, Reminder.TYPE_WEIGHT, "08:00", "标题", "");
        assertFalse("空内容的提醒应该无效", invalidReminder4.isValid());
        
        // 测试有效提醒数据
        Reminder validReminder = new Reminder(1, Reminder.TYPE_MEAL, "12:30", "午餐提醒", "该吃午餐了！");
        assertTrue("有效提醒应该通过验证", validReminder.isValid());
    }
    
    @Test
    public void testDatabaseManager() {
        // 测试创建用户
        User user = dbManager.createOrUpdateUser("赵六", "female", 28, 168.0, 58.0, 52.0);
        assertNotNull("应该能创建用户", user);
        assertEquals("用户名应该正确", "赵六", user.getName());
        
        // 测试获取当前用户
        User currentUser = dbManager.getCurrentUser();
        assertNotNull("应该能获取当前用户", currentUser);
        assertEquals("当前用户应该是刚创建的用户", user.getId(), currentUser.getId());
        
        // 测试保存计算结果
        boolean saveResult = dbManager.saveCalculationResult(57.5, 1280.0, -250.0, 
                                                            "建议适量减少碳水摄入", "light");
        assertTrue("计算结果保存应该成功", saveResult);
        
        // 测试获取历史记录
        List<HistoryRecord> history = dbManager.getRecentHistory(5);
        assertNotNull("应该能获取历史记录", history);
        assertEquals("历史记录数量应该正确", 1, history.size());
        
        HistoryRecord latestRecord = dbManager.getLatestHistory();
        assertNotNull("应该能获取最新记录", latestRecord);
        assertEquals("最新记录的体重应该正确", 57.5, latestRecord.getWeight(), 0.01);
        
        // 测试设置提醒
        boolean reminderResult = dbManager.setWeightReminder("07:30", true);
        assertTrue("设置体重提醒应该成功", reminderResult);
        
        List<Reminder> reminders = dbManager.getUserReminders();
        assertNotNull("应该能获取提醒列表", reminders);
        assertEquals("提醒数量应该正确", 1, reminders.size());
        
        Reminder weightReminder = dbManager.getReminderByType(Reminder.TYPE_WEIGHT);
        assertNotNull("应该能获取体重提醒", weightReminder);
        assertEquals("体重提醒时间应该正确", "07:30", weightReminder.getTime());
        assertTrue("体重提醒应该启用", weightReminder.isEnabled());
    }
    
    @Test
    public void testForeignKeyConstraints() {
        // 创建用户
        User user = new User("测试用户", "male", 25, 175.0, 70.0, 65.0);
        long userId = dbHelper.insertUser(user);
        
        // 创建历史记录
        HistoryRecord record = new HistoryRecord(userId, 70.0, 1500.0, -200.0, "测试建议", "moderate");
        long recordId = dbHelper.insertHistory(record);
        assertTrue("历史记录创建应该成功", recordId > 0);
        
        // 创建提醒
        Reminder reminder = new Reminder(userId, Reminder.TYPE_WEIGHT, "08:00", "测试提醒", "测试内容");
        long reminderId = dbHelper.insertReminder(reminder);
        assertTrue("提醒创建应该成功", reminderId > 0);
        
        // 删除用户（应该级联删除相关记录）
        boolean deleteResult = dbHelper.deleteUser(userId);
        assertTrue("用户删除应该成功", deleteResult);
        
        // 验证相关记录也被删除
        List<HistoryRecord> historyAfterDelete = dbHelper.getHistoryList(userId, 10);
        assertEquals("用户删除后历史记录应该为空", 0, historyAfterDelete.size());
        
        List<Reminder> remindersAfterDelete = dbHelper.getReminders(userId);
        assertEquals("用户删除后提醒应该为空", 0, remindersAfterDelete.size());
    }
}
