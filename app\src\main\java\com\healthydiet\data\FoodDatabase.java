package com.healthydiet.data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 食物数据库类
 * 提供常见食物的营养成分数据和推荐食物组合
 */
public class FoodDatabase {
    
    // 食物分类常量
    public static final String CATEGORY_PROTEIN = "protein"; // 蛋白质类
    public static final String CATEGORY_CARB = "carbohydrate"; // 碳水化合物类
    public static final String CATEGORY_FAT = "fat"; // 脂肪类
    public static final String CATEGORY_VEGETABLE = "vegetable"; // 蔬菜类
    public static final String CATEGORY_FRUIT = "fruit"; // 水果类
    public static final String CATEGORY_DAIRY = "dairy"; // 乳制品类
    public static final String CATEGORY_NUTS = "nuts"; // 坚果类
    public static final String CATEGORY_BEVERAGE = "beverage"; // 饮品类
    
    // 餐次常量
    public static final String MEAL_BREAKFAST = "breakfast"; // 早餐
    public static final String MEAL_LUNCH = "lunch"; // 午餐
    public static final String MEAL_DINNER = "dinner"; // 晚餐
    public static final String MEAL_SNACK = "snack"; // 加餐
    
    private static final Map<String, FoodItem> foodDatabase = new HashMap<>();
    private static final Map<String, List<String>> categoryFoods = new HashMap<>();
    private static final Map<String, List<String>> mealRecommendations = new HashMap<>();
    
    static {
        initializeFoodDatabase();
        initializeCategoryMappings();
        initializeMealRecommendations();
    }
    
    /**
     * 初始化食物数据库
     */
    private static void initializeFoodDatabase() {
        // 蛋白质类食物 (每100g营养成分)
        addFood("鸡胸肉", CATEGORY_PROTEIN, 165, 31.0, 0.0, 3.6, "优质蛋白质来源，脂肪含量低");
        addFood("鸡蛋", CATEGORY_PROTEIN, 155, 13.0, 1.1, 11.0, "完全蛋白质，营养价值高");
        addFood("牛肉", CATEGORY_PROTEIN, 250, 26.0, 0.0, 15.0, "富含铁质和优质蛋白质");
        addFood("鱼肉", CATEGORY_PROTEIN, 206, 22.0, 0.0, 12.0, "富含Omega-3脂肪酸");
        addFood("豆腐", CATEGORY_PROTEIN, 76, 8.0, 1.9, 4.8, "植物蛋白质，适合素食者");
        addFood("瘦猪肉", CATEGORY_PROTEIN, 143, 20.0, 0.0, 6.2, "优质蛋白质来源");
        
        // 碳水化合物类食物
        addFood("白米饭", CATEGORY_CARB, 130, 2.7, 28.0, 0.3, "主要能量来源");
        addFood("全麦面包", CATEGORY_CARB, 247, 13.0, 41.0, 4.2, "富含膳食纤维");
        addFood("燕麦", CATEGORY_CARB, 389, 16.9, 66.3, 6.9, "富含β-葡聚糖，有助降胆固醇");
        addFood("红薯", CATEGORY_CARB, 86, 1.6, 20.1, 0.1, "富含维生素A和膳食纤维");
        addFood("土豆", CATEGORY_CARB, 77, 2.0, 17.5, 0.1, "富含钾和维生素C");
        addFood("糙米", CATEGORY_CARB, 111, 2.6, 23.0, 0.9, "保留胚芽，营养更丰富");
        
        // 蔬菜类食物
        addFood("西兰花", CATEGORY_VEGETABLE, 34, 2.8, 6.6, 0.4, "富含维生素C和叶酸");
        addFood("菠菜", CATEGORY_VEGETABLE, 23, 2.9, 3.6, 0.4, "富含铁质和叶酸");
        addFood("胡萝卜", CATEGORY_VEGETABLE, 41, 0.9, 9.6, 0.2, "富含β-胡萝卜素");
        addFood("白菜", CATEGORY_VEGETABLE, 13, 1.5, 2.2, 0.1, "低热量，富含维生素C");
        addFood("番茄", CATEGORY_VEGETABLE, 18, 0.9, 3.9, 0.2, "富含番茄红素");
        addFood("黄瓜", CATEGORY_VEGETABLE, 15, 0.7, 3.6, 0.1, "水分含量高，低热量");
        
        // 水果类食物
        addFood("苹果", CATEGORY_FRUIT, 52, 0.3, 13.8, 0.2, "富含膳食纤维和维生素C");
        addFood("香蕉", CATEGORY_FRUIT, 89, 1.1, 22.8, 0.3, "富含钾和维生素B6");
        addFood("橙子", CATEGORY_FRUIT, 47, 0.9, 11.8, 0.1, "富含维生素C");
        addFood("草莓", CATEGORY_FRUIT, 32, 0.7, 7.7, 0.3, "富含维生素C和抗氧化物");
        addFood("蓝莓", CATEGORY_FRUIT, 57, 0.7, 14.5, 0.3, "富含花青素，抗氧化效果好");
        addFood("葡萄", CATEGORY_FRUIT, 62, 0.6, 16.0, 0.2, "富含白藜芦醇");
        
        // 乳制品类食物
        addFood("牛奶", CATEGORY_DAIRY, 42, 3.4, 5.0, 1.0, "富含钙质和优质蛋白质");
        addFood("酸奶", CATEGORY_DAIRY, 59, 3.5, 4.7, 3.3, "含有益生菌，有助消化");
        addFood("奶酪", CATEGORY_DAIRY, 113, 7.0, 1.0, 9.0, "高蛋白质，富含钙质");
        
        // 坚果类食物
        addFood("杏仁", CATEGORY_NUTS, 579, 21.2, 21.6, 49.9, "富含维生素E和健康脂肪");
        addFood("核桃", CATEGORY_NUTS, 654, 15.2, 13.7, 65.2, "富含Omega-3脂肪酸");
        addFood("花生", CATEGORY_NUTS, 567, 26.0, 16.1, 49.2, "富含蛋白质和烟酸");
        
        // 脂肪类食物
        addFood("橄榄油", CATEGORY_FAT, 884, 0.0, 0.0, 100.0, "富含单不饱和脂肪酸");
        addFood("牛油果", CATEGORY_FAT, 160, 2.0, 8.5, 14.7, "富含健康脂肪和膳食纤维");
        
        // 饮品类
        addFood("绿茶", CATEGORY_BEVERAGE, 1, 0.0, 0.0, 0.0, "富含抗氧化物，无热量");
        addFood("黑咖啡", CATEGORY_BEVERAGE, 2, 0.3, 0.0, 0.0, "含咖啡因，几乎无热量");
    }
    
    /**
     * 添加食物到数据库
     */
    private static void addFood(String name, String category, double calories, 
                               double protein, double carbs, double fat, String description) {
        FoodItem food = new FoodItem(name, category, calories, protein, carbs, fat, description);
        foodDatabase.put(name, food);
    }
    
    /**
     * 初始化分类映射
     */
    private static void initializeCategoryMappings() {
        categoryFoods.put(CATEGORY_PROTEIN, Arrays.asList(
            "鸡胸肉", "鸡蛋", "牛肉", "鱼肉", "豆腐", "瘦猪肉"
        ));
        
        categoryFoods.put(CATEGORY_CARB, Arrays.asList(
            "白米饭", "全麦面包", "燕麦", "红薯", "土豆", "糙米"
        ));
        
        categoryFoods.put(CATEGORY_VEGETABLE, Arrays.asList(
            "西兰花", "菠菜", "胡萝卜", "白菜", "番茄", "黄瓜"
        ));
        
        categoryFoods.put(CATEGORY_FRUIT, Arrays.asList(
            "苹果", "香蕉", "橙子", "草莓", "蓝莓", "葡萄"
        ));
        
        categoryFoods.put(CATEGORY_DAIRY, Arrays.asList(
            "牛奶", "酸奶", "奶酪"
        ));
        
        categoryFoods.put(CATEGORY_NUTS, Arrays.asList(
            "杏仁", "核桃", "花生"
        ));
        
        categoryFoods.put(CATEGORY_FAT, Arrays.asList(
            "橄榄油", "牛油果"
        ));
        
        categoryFoods.put(CATEGORY_BEVERAGE, Arrays.asList(
            "绿茶", "黑咖啡"
        ));
    }
    
    /**
     * 初始化餐次推荐
     */
    private static void initializeMealRecommendations() {
        // 早餐推荐
        mealRecommendations.put(MEAL_BREAKFAST, Arrays.asList(
            "鸡蛋", "牛奶", "燕麦", "全麦面包", "香蕉", "苹果"
        ));
        
        // 午餐推荐
        mealRecommendations.put(MEAL_LUNCH, Arrays.asList(
            "鸡胸肉", "牛肉", "鱼肉", "白米饭", "糙米", "西兰花", "胡萝卜"
        ));
        
        // 晚餐推荐
        mealRecommendations.put(MEAL_DINNER, Arrays.asList(
            "鱼肉", "豆腐", "瘦猪肉", "红薯", "菠菜", "番茄", "白菜"
        ));
        
        // 加餐推荐
        mealRecommendations.put(MEAL_SNACK, Arrays.asList(
            "酸奶", "杏仁", "核桃", "苹果", "草莓", "蓝莓"
        ));
    }
    
    /**
     * 根据名称获取食物信息
     * @param foodName 食物名称
     * @return 食物信息，不存在返回null
     */
    public static FoodItem getFoodByName(String foodName) {
        return foodDatabase.get(foodName);
    }
    
    /**
     * 根据分类获取食物列表
     * @param category 食物分类
     * @return 食物名称列表
     */
    public static List<String> getFoodsByCategory(String category) {
        return categoryFoods.getOrDefault(category, new ArrayList<>());
    }
    
    /**
     * 根据餐次获取推荐食物
     * @param meal 餐次
     * @return 推荐食物列表
     */
    public static List<String> getRecommendedFoodsForMeal(String meal) {
        return mealRecommendations.getOrDefault(meal, new ArrayList<>());
    }
    
    /**
     * 获取所有食物名称
     * @return 所有食物名称列表
     */
    public static List<String> getAllFoodNames() {
        return new ArrayList<>(foodDatabase.keySet());
    }
    
    /**
     * 获取所有食物分类
     * @return 所有分类列表
     */
    public static List<String> getAllCategories() {
        return new ArrayList<>(categoryFoods.keySet());
    }
    
    /**
     * 根据营养需求推荐食物
     * @param targetProtein 目标蛋白质 (g)
     * @param targetCarbs 目标碳水化合物 (g)
     * @param targetFat 目标脂肪 (g)
     * @param meal 餐次
     * @return 推荐食物组合
     */
    public static List<FoodRecommendation> recommendFoodsForNutrition(double targetProtein, 
                                                                     double targetCarbs, 
                                                                     double targetFat, 
                                                                     String meal) {
        List<FoodRecommendation> recommendations = new ArrayList<>();
        
        // 获取该餐次的推荐食物
        List<String> mealFoods = getRecommendedFoodsForMeal(meal);
        
        // 按营养素需求推荐食物
        if (targetProtein > 0) {
            String proteinFood = findBestProteinSource(mealFoods, targetProtein);
            if (proteinFood != null) {
                FoodItem food = getFoodByName(proteinFood);
                double portion = calculatePortion(targetProtein, food.getProteinPer100g());
                recommendations.add(new FoodRecommendation(proteinFood, portion, "蛋白质来源"));
            }
        }
        
        if (targetCarbs > 0) {
            String carbFood = findBestCarbSource(mealFoods, targetCarbs);
            if (carbFood != null) {
                FoodItem food = getFoodByName(carbFood);
                double portion = calculatePortion(targetCarbs, food.getCarbsPer100g());
                recommendations.add(new FoodRecommendation(carbFood, portion, "碳水化合物来源"));
            }
        }
        
        if (targetFat > 0) {
            String fatFood = findBestFatSource(mealFoods, targetFat);
            if (fatFood != null) {
                FoodItem food = getFoodByName(fatFood);
                double portion = calculatePortion(targetFat, food.getFatPer100g());
                recommendations.add(new FoodRecommendation(fatFood, portion, "脂肪来源"));
            }
        }
        
        // 添加蔬菜推荐
        addVegetableRecommendations(recommendations, meal);
        
        return recommendations;
    }
    
    /**
     * 寻找最佳蛋白质来源
     */
    private static String findBestProteinSource(List<String> availableFoods, double targetProtein) {
        String bestFood = null;
        double bestScore = 0;
        
        for (String foodName : availableFoods) {
            FoodItem food = getFoodByName(foodName);
            if (food != null && food.getProteinPer100g() > 10) { // 蛋白质含量>10g/100g
                double score = food.getProteinPer100g() / food.getCaloriesPer100g() * 100; // 蛋白质密度
                if (score > bestScore) {
                    bestScore = score;
                    bestFood = foodName;
                }
            }
        }
        
        // 如果没有找到合适的，从蛋白质类别中选择
        if (bestFood == null) {
            List<String> proteinFoods = getFoodsByCategory(CATEGORY_PROTEIN);
            for (String foodName : proteinFoods) {
                FoodItem food = getFoodByName(foodName);
                if (food != null) {
                    double score = food.getProteinPer100g() / food.getCaloriesPer100g() * 100;
                    if (score > bestScore) {
                        bestScore = score;
                        bestFood = foodName;
                    }
                }
            }
        }
        
        return bestFood;
    }
    
    /**
     * 寻找最佳碳水化合物来源
     */
    private static String findBestCarbSource(List<String> availableFoods, double targetCarbs) {
        String bestFood = null;
        double bestScore = 0;
        
        for (String foodName : availableFoods) {
            FoodItem food = getFoodByName(foodName);
            if (food != null && food.getCarbsPer100g() > 15) { // 碳水含量>15g/100g
                double score = food.getCarbsPer100g();
                if (score > bestScore) {
                    bestScore = score;
                    bestFood = foodName;
                }
            }
        }
        
        // 如果没有找到合适的，从碳水类别中选择
        if (bestFood == null) {
            List<String> carbFoods = getFoodsByCategory(CATEGORY_CARB);
            for (String foodName : carbFoods) {
                FoodItem food = getFoodByName(foodName);
                if (food != null) {
                    double score = food.getCarbsPer100g();
                    if (score > bestScore) {
                        bestScore = score;
                        bestFood = foodName;
                    }
                }
            }
        }
        
        return bestFood;
    }
    
    /**
     * 寻找最佳脂肪来源
     */
    private static String findBestFatSource(List<String> availableFoods, double targetFat) {
        String bestFood = null;
        double bestScore = 0;
        
        for (String foodName : availableFoods) {
            FoodItem food = getFoodByName(foodName);
            if (food != null && food.getFatPer100g() > 5) { // 脂肪含量>5g/100g
                double score = food.getFatPer100g();
                if (score > bestScore) {
                    bestScore = score;
                    bestFood = foodName;
                }
            }
        }
        
        // 如果没有找到合适的，推荐健康脂肪
        if (bestFood == null) {
            List<String> fatFoods = getFoodsByCategory(CATEGORY_FAT);
            List<String> nutFoods = getFoodsByCategory(CATEGORY_NUTS);
            
            for (String foodName : fatFoods) {
                FoodItem food = getFoodByName(foodName);
                if (food != null) {
                    bestFood = foodName;
                    break;
                }
            }
            
            if (bestFood == null && !nutFoods.isEmpty()) {
                bestFood = nutFoods.get(0); // 选择第一个坚果
            }
        }
        
        return bestFood;
    }
    
    /**
     * 添加蔬菜推荐
     */
    private static void addVegetableRecommendations(List<FoodRecommendation> recommendations, String meal) {
        List<String> vegetables = getFoodsByCategory(CATEGORY_VEGETABLE);
        if (!vegetables.isEmpty()) {
            String vegetable = vegetables.get(0); // 选择第一个蔬菜
            recommendations.add(new FoodRecommendation(vegetable, 150.0, "蔬菜补充"));
        }
    }
    
    /**
     * 计算食物分量
     * @param targetNutrient 目标营养素量
     * @param nutrientPer100g 每100g营养素含量
     * @return 所需分量 (g)
     */
    private static double calculatePortion(double targetNutrient, double nutrientPer100g) {
        if (nutrientPer100g <= 0) return 0;
        return Math.max(50, Math.min(300, (targetNutrient / nutrientPer100g) * 100)); // 限制在50-300g之间
    }
    
    /**
     * 食物信息类
     */
    public static class FoodItem {
        private String name;
        private String category;
        private double caloriesPer100g;
        private double proteinPer100g;
        private double carbsPer100g;
        private double fatPer100g;
        private String description;
        
        public FoodItem(String name, String category, double calories, double protein, 
                       double carbs, double fat, String description) {
            this.name = name;
            this.category = category;
            this.caloriesPer100g = calories;
            this.proteinPer100g = protein;
            this.carbsPer100g = carbs;
            this.fatPer100g = fat;
            this.description = description;
        }
        
        // Getters
        public String getName() { return name; }
        public String getCategory() { return category; }
        public double getCaloriesPer100g() { return caloriesPer100g; }
        public double getProteinPer100g() { return proteinPer100g; }
        public double getCarbsPer100g() { return carbsPer100g; }
        public double getFatPer100g() { return fatPer100g; }
        public String getDescription() { return description; }
        
        /**
         * 计算指定分量的营养成分
         * @param portionGrams 分量 (g)
         * @return 营养成分数组 [热量, 蛋白质, 碳水, 脂肪]
         */
        public double[] getNutritionForPortion(double portionGrams) {
            double factor = portionGrams / 100.0;
            return new double[]{
                caloriesPer100g * factor,
                proteinPer100g * factor,
                carbsPer100g * factor,
                fatPer100g * factor
            };
        }
        
        @Override
        public String toString() {
            return String.format("%s: %.0f卡/100g (蛋白质%.1fg, 碳水%.1fg, 脂肪%.1fg)", 
                               name, caloriesPer100g, proteinPer100g, carbsPer100g, fatPer100g);
        }
    }
    
    /**
     * 食物推荐类
     */
    public static class FoodRecommendation {
        private String foodName;
        private double portionGrams;
        private String reason;
        
        public FoodRecommendation(String foodName, double portionGrams, String reason) {
            this.foodName = foodName;
            this.portionGrams = portionGrams;
            this.reason = reason;
        }
        
        public String getFoodName() { return foodName; }
        public double getPortionGrams() { return portionGrams; }
        public String getReason() { return reason; }
        
        /**
         * 获取该推荐的营养成分
         * @return 营养成分数组 [热量, 蛋白质, 碳水, 脂肪]
         */
        public double[] getNutrition() {
            FoodItem food = getFoodByName(foodName);
            if (food != null) {
                return food.getNutritionForPortion(portionGrams);
            }
            return new double[]{0, 0, 0, 0};
        }
        
        @Override
        public String toString() {
            return String.format("%s %.0fg (%s)", foodName, portionGrams, reason);
        }
    }
}
