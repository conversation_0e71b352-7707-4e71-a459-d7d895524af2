package com.healthydiet.database;

import android.content.Context;
import android.util.Log;

import com.healthydiet.model.HistoryRecord;
import com.healthydiet.model.Reminder;
import com.healthydiet.model.User;

import java.util.Date;
import java.util.List;

/**
 * 数据库管理器
 * 提供高级的数据访问接口，封装常用的数据库操作
 */
public class DatabaseManager {
    private static final String TAG = "DatabaseManager";
    private static DatabaseManager instance;
    private DatabaseHelper dbHelper;
    
    private DatabaseManager(Context context) {
        dbHelper = DatabaseHelper.getInstance(context);
    }
    
    /**
     * 获取数据库管理器的单例实例
     * @param context 上下文
     * @return DatabaseManager实例
     */
    public static synchronized DatabaseManager getInstance(Context context) {
        if (instance == null) {
            instance = new DatabaseManager(context);
        }
        return instance;
    }
    
    // ==================== 用户管理 ====================
    
    /**
     * 创建或更新用户信息
     * @param name 姓名
     * @param gender 性别
     * @param age 年龄
     * @param height 身高
     * @param weight 体重
     * @param targetWeight 目标体重
     * @return 用户对象
     */
    public User createOrUpdateUser(String name, String gender, int age, 
                                  double height, double weight, double targetWeight) {
        User existingUser = getCurrentUser();
        
        if (existingUser != null) {
            // 更新现有用户
            existingUser.setName(name);
            existingUser.setGender(gender);
            existingUser.setAge(age);
            existingUser.setHeight(height);
            existingUser.setWeight(weight);
            existingUser.setTargetWeight(targetWeight);
            
            if (dbHelper.updateUser(existingUser)) {
                Log.d(TAG, "用户信息更新成功");
                return existingUser;
            } else {
                Log.e(TAG, "用户信息更新失败");
                return null;
            }
        } else {
            // 创建新用户
            User newUser = new User(name, gender, age, height, weight, targetWeight);
            long userId = dbHelper.insertUser(newUser);
            
            if (userId != -1) {
                Log.d(TAG, "新用户创建成功，ID: " + userId);
                return newUser;
            } else {
                Log.e(TAG, "新用户创建失败");
                return null;
            }
        }
    }
    
    /**
     * 获取当前用户（应用通常只有一个用户）
     * @return 当前用户，不存在返回null
     */
    public User getCurrentUser() {
        return dbHelper.getFirstUser();
    }
    
    /**
     * 更新用户体重
     * @param newWeight 新体重
     * @return 更新成功返回true
     */
    public boolean updateUserWeight(double newWeight) {
        User user = getCurrentUser();
        if (user != null) {
            user.setWeight(newWeight);
            return dbHelper.updateUser(user);
        }
        return false;
    }
    
    /**
     * 检查是否有用户数据
     * @return 有用户返回true
     */
    public boolean hasUser() {
        return dbHelper.getUserCount() > 0;
    }
    
    // ==================== 历史记录管理 ====================
    
    /**
     * 保存计算结果到历史记录
     * @param weight 体重
     * @param bmr BMR值
     * @param calorieDeficit 热量缺口
     * @param suggestion 饮食建议
     * @param activityLevel 运动强度
     * @return 保存成功返回true
     */
    public boolean saveCalculationResult(double weight, double bmr, double calorieDeficit, 
                                       String suggestion, String activityLevel) {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法保存历史记录");
            return false;
        }
        
        HistoryRecord record = new HistoryRecord(user.getId(), weight, bmr, calorieDeficit, 
                                               suggestion, activityLevel);
        long recordId = dbHelper.insertHistory(record);
        
        if (recordId != -1) {
            // 同时更新用户的当前体重
            updateUserWeight(weight);
            Log.d(TAG, "计算结果保存成功");
            return true;
        } else {
            Log.e(TAG, "计算结果保存失败");
            return false;
        }
    }
    
    /**
     * 获取最近的历史记录
     * @param limit 限制数量
     * @return 历史记录列表
     */
    public List<HistoryRecord> getRecentHistory(int limit) {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法获取历史记录");
            return null;
        }
        
        return dbHelper.getHistoryList(user.getId(), limit);
    }
    
    /**
     * 获取所有历史记录
     * @return 历史记录列表
     */
    public List<HistoryRecord> getAllHistory() {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法获取历史记录");
            return null;
        }
        
        return dbHelper.getAllHistory(user.getId());
    }
    
    /**
     * 获取最新的历史记录
     * @return 最新的历史记录，不存在返回null
     */
    public HistoryRecord getLatestHistory() {
        List<HistoryRecord> recentHistory = getRecentHistory(1);
        if (recentHistory != null && !recentHistory.isEmpty()) {
            return recentHistory.get(0);
        }
        return null;
    }
    
    /**
     * 删除历史记录
     * @param recordId 记录ID
     * @return 删除成功返回true
     */
    public boolean deleteHistoryRecord(long recordId) {
        return dbHelper.deleteHistory(recordId);
    }
    
    // ==================== 提醒管理 ====================
    
    /**
     * 设置体重记录提醒
     * @param time 提醒时间 (HH:mm格式)
     * @param enabled 是否启用
     * @return 设置成功返回true
     */
    public boolean setWeightReminder(String time, boolean enabled) {
        return setReminder(Reminder.TYPE_WEIGHT, time, "体重记录提醒", 
                         "该记录今天的体重了！", enabled);
    }
    
    /**
     * 设置饮食记录提醒
     * @param time 提醒时间 (HH:mm格式)
     * @param enabled 是否启用
     * @return 设置成功返回true
     */
    public boolean setMealReminder(String time, boolean enabled) {
        return setReminder(Reminder.TYPE_MEAL, time, "饮食记录提醒", 
                         "记得记录今天的饮食情况！", enabled);
    }
    
    /**
     * 设置运动提醒
     * @param time 提醒时间 (HH:mm格式)
     * @param enabled 是否启用
     * @return 设置成功返回true
     */
    public boolean setExerciseReminder(String time, boolean enabled) {
        return setReminder(Reminder.TYPE_EXERCISE, time, "运动提醒", 
                         "该进行今天的运动了！", enabled);
    }
    
    /**
     * 设置提醒
     * @param type 提醒类型
     * @param time 提醒时间
     * @param title 提醒标题
     * @param message 提醒内容
     * @param enabled 是否启用
     * @return 设置成功返回true
     */
    private boolean setReminder(String type, String time, String title, String message, boolean enabled) {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法设置提醒");
            return false;
        }
        
        // 检查是否已存在相同类型的提醒
        List<Reminder> existingReminders = getUserReminders();
        Reminder existingReminder = null;
        
        for (Reminder reminder : existingReminders) {
            if (type.equals(reminder.getType())) {
                existingReminder = reminder;
                break;
            }
        }
        
        if (existingReminder != null) {
            // 更新现有提醒
            existingReminder.setTime(time);
            existingReminder.setEnabled(enabled);
            existingReminder.setTitle(title);
            existingReminder.setMessage(message);
            return dbHelper.updateReminder(existingReminder);
        } else {
            // 创建新提醒
            Reminder newReminder = new Reminder(user.getId(), type, time, title, message);
            newReminder.setEnabled(enabled);
            long reminderId = dbHelper.insertReminder(newReminder);
            return reminderId != -1;
        }
    }
    
    /**
     * 获取用户的所有提醒
     * @return 提醒列表
     */
    public List<Reminder> getUserReminders() {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法获取提醒");
            return null;
        }
        
        return dbHelper.getReminders(user.getId());
    }
    
    /**
     * 获取启用的提醒
     * @return 启用的提醒列表
     */
    public List<Reminder> getEnabledReminders() {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法获取提醒");
            return null;
        }
        
        return dbHelper.getEnabledReminders(user.getId());
    }
    
    /**
     * 获取指定类型的提醒
     * @param type 提醒类型
     * @return 提醒对象，不存在返回null
     */
    public Reminder getReminderByType(String type) {
        List<Reminder> reminders = getUserReminders();
        if (reminders != null) {
            for (Reminder reminder : reminders) {
                if (type.equals(reminder.getType())) {
                    return reminder;
                }
            }
        }
        return null;
    }
    
    /**
     * 删除提醒
     * @param reminderId 提醒ID
     * @return 删除成功返回true
     */
    public boolean deleteReminder(long reminderId) {
        return dbHelper.deleteReminder(reminderId);
    }
    
    /**
     * 切换提醒的启用状态
     * @param reminderId 提醒ID
     * @return 切换成功返回true
     */
    public boolean toggleReminderEnabled(long reminderId) {
        List<Reminder> reminders = getUserReminders();
        if (reminders != null) {
            for (Reminder reminder : reminders) {
                if (reminder.getId() == reminderId) {
                    reminder.setEnabled(!reminder.isEnabled());
                    return dbHelper.updateReminder(reminder);
                }
            }
        }
        return false;
    }
    
    // ==================== 数据统计 ====================
    
    /**
     * 获取体重变化趋势数据
     * @param days 最近天数
     * @return 历史记录列表，按日期排序
     */
    public List<HistoryRecord> getWeightTrend(int days) {
        List<HistoryRecord> allHistory = getAllHistory();
        if (allHistory == null || allHistory.isEmpty()) {
            return null;
        }
        
        // 如果记录数少于指定天数，返回所有记录
        if (allHistory.size() <= days) {
            return allHistory;
        }
        
        // 返回最近指定天数的记录
        return allHistory.subList(0, days);
    }
    
    /**
     * 计算平均BMR
     * @param days 最近天数
     * @return 平均BMR值
     */
    public double getAverageBMR(int days) {
        List<HistoryRecord> recentHistory = getRecentHistory(days);
        if (recentHistory == null || recentHistory.isEmpty()) {
            return 0;
        }
        
        double totalBMR = 0;
        for (HistoryRecord record : recentHistory) {
            totalBMR += record.getBmr();
        }
        
        return totalBMR / recentHistory.size();
    }
    
    /**
     * 计算平均热量缺口
     * @param days 最近天数
     * @return 平均热量缺口
     */
    public double getAverageCalorieDeficit(int days) {
        List<HistoryRecord> recentHistory = getRecentHistory(days);
        if (recentHistory == null || recentHistory.isEmpty()) {
            return 0;
        }
        
        double totalDeficit = 0;
        for (HistoryRecord record : recentHistory) {
            totalDeficit += record.getCalorieDeficit();
        }
        
        return totalDeficit / recentHistory.size();
    }
    
    // ==================== 数据管理 ====================
    
    /**
     * 初始化默认提醒
     */
    public void initializeDefaultReminders() {
        User user = getCurrentUser();
        if (user == null) {
            Log.e(TAG, "没有找到用户，无法初始化默认提醒");
            return;
        }
        
        // 设置默认的体重记录提醒（早上8点）
        setWeightReminder("08:00", true);
        
        // 设置默认的饮食记录提醒（晚上8点）
        setMealReminder("20:00", true);
        
        Log.d(TAG, "默认提醒初始化完成");
    }
    
    /**
     * 清空所有数据
     */
    public void clearAllData() {
        dbHelper.clearAllData();
        Log.d(TAG, "所有数据已清空");
    }
    
    /**
     * 检查数据库连接
     * @return 连接正常返回true
     */
    public boolean checkDatabaseConnection() {
        try {
            int userCount = dbHelper.getUserCount();
            Log.d(TAG, "数据库连接正常，用户数量: " + userCount);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "数据库连接异常", e);
            return false;
        }
    }
}
