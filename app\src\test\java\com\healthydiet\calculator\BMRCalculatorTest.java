package com.healthydiet.calculator;

import static org.junit.Assert.*;

import com.healthydiet.model.User;

import org.junit.Test;

/**
 * BMR计算器单元测试
 * 验证Harris-Benedict公式的计算准确性
 */
public class BMRCalculatorTest {
    
    private static final double DELTA = 0.1; // 允许的误差范围
    
    @Test
    public void testMaleBMRCalculation() {
        // 测试男性BMR计算
        // 参考值：25岁男性，70kg，175cm
        // 预期BMR = 66 + (13.7 × 70) + (5 × 175) - (6.8 × 25) = 66 + 959 + 875 - 170 = 1730
        double bmr = BMRCalculator.calculateBMR("male", 25, 70.0, 175.0);
        assertEquals("男性BMR计算应该准确", 1730.0, bmr, DELTA);
    }
    
    @Test
    public void testFemaleBMRCalculation() {
        // 测试女性BMR计算
        // 参考值：30岁女性，60kg，165cm
        // 预期BMR = 655 + (9.6 × 60) + (1.8 × 165) - (4.7 × 30) = 655 + 576 + 297 - 141 = 1387
        double bmr = BMRCalculator.calculateBMR("female", 30, 60.0, 165.0);
        assertEquals("女性BMR计算应该准确", 1387.0, bmr, DELTA);
    }
    
    @Test
    public void testMifflinBMRCalculation() {
        // 测试Mifflin-St Jeor公式
        // 男性：25岁，70kg，175cm
        // 预期BMR = (10 × 70) + (6.25 × 175) - (5 × 25) + 5 = 700 + 1093.75 - 125 + 5 = 1673.75
        double bmr = BMRCalculator.calculateBMRMifflin("male", 25, 70.0, 175.0);
        assertEquals("男性Mifflin BMR计算应该准确", 1673.8, bmr, DELTA);
        
        // 女性：30岁，60kg，165cm
        // 预期BMR = (10 × 60) + (6.25 × 165) - (5 × 30) - 161 = 600 + 1031.25 - 150 - 161 = 1320.25
        double femaleBmr = BMRCalculator.calculateBMRMifflin("female", 30, 60.0, 165.0);
        assertEquals("女性Mifflin BMR计算应该准确", 1320.3, femaleBmr, DELTA);
    }
    
    @Test
    public void testBMRWithUserObject() {
        // 测试使用User对象计算BMR
        User user = new User("测试用户", "male", 28, 180.0, 75.0, 70.0);
        double bmr = BMRCalculator.calculateBMR(user);
        
        // 手动计算验证：66 + (13.7 × 75) + (5 × 180) - (6.8 × 28) = 66 + 1027.5 + 900 - 190.4 = 1803.1
        assertEquals("使用User对象的BMR计算应该准确", 1803.1, bmr, DELTA);
    }
    
    @Test
    public void testInvalidInputs() {
        // 测试无效输入
        assertEquals("无效性别应该返回-1", -1.0, BMRCalculator.calculateBMR("unknown", 25, 70.0, 175.0), DELTA);
        assertEquals("无效年龄应该返回-1", -1.0, BMRCalculator.calculateBMR("male", 0, 70.0, 175.0), DELTA);
        assertEquals("无效体重应该返回-1", -1.0, BMRCalculator.calculateBMR("male", 25, 0.0, 175.0), DELTA);
        assertEquals("无效身高应该返回-1", -1.0, BMRCalculator.calculateBMR("male", 25, 70.0, 0.0), DELTA);
        
        // 测试空用户对象
        assertEquals("空用户对象应该返回-1", -1.0, BMRCalculator.calculateBMR((User) null), DELTA);
    }
    
    @Test
    public void testBMRRange() {
        // 测试BMR范围计算
        double[] range = BMRCalculator.calculateBMRRange("male", 25, 70.0, 175.0);
        assertNotNull("BMR范围不应该为空", range);
        assertEquals("BMR范围应该有两个值", 2, range.length);
        
        double baseBMR = BMRCalculator.calculateBMR("male", 25, 70.0, 175.0);
        double expectedMin = baseBMR * 0.9;
        double expectedMax = baseBMR * 1.1;
        
        assertEquals("BMR最小值应该正确", expectedMin, range[0], DELTA);
        assertEquals("BMR最大值应该正确", expectedMax, range[1], DELTA);
    }
    
    @Test
    public void testBMRFormulaComparison() {
        // 测试两种公式的比较
        double[] comparison = BMRCalculator.compareBMRFormulas("male", 25, 70.0, 175.0);
        assertNotNull("公式比较结果不应该为空", comparison);
        assertEquals("比较结果应该有三个值", 3, comparison.length);
        
        double harrisBMR = comparison[0];
        double mifflinBMR = comparison[1];
        double difference = comparison[2];
        
        assertTrue("Harris-Benedict BMR应该大于0", harrisBMR > 0);
        assertTrue("Mifflin-St Jeor BMR应该大于0", mifflinBMR > 0);
        assertEquals("差值应该等于两个BMR的绝对差", Math.abs(harrisBMR - mifflinBMR), difference, DELTA);
    }
    
    @Test
    public void testIdealWeightEstimation() {
        // 测试理想体重估算
        double targetBMR = 1700.0;
        double[] weightRange = BMRCalculator.estimateIdealWeightRange("male", 25, 175.0, targetBMR);
        
        assertNotNull("理想体重范围不应该为空", weightRange);
        assertEquals("理想体重范围应该有两个值", 2, weightRange.length);
        assertTrue("最小体重应该大于0", weightRange[0] > 0);
        assertTrue("最大体重应该大于最小体重", weightRange[1] > weightRange[0]);
        assertTrue("体重范围应该合理", weightRange[0] >= 50 && weightRange[1] <= 120);
    }
    
    @Test
    public void testBMRValidation() {
        // 测试BMR结果验证
        double validBMR = 1500.0;
        String validation = BMRCalculator.validateBMRResult(validBMR, "male", 25, 70.0);
        assertNotNull("验证结果不应该为空", validation);
        assertTrue("有效BMR应该通过验证", validation.contains("合理范围"));
        
        double invalidBMR = 100.0; // 过低的BMR
        String invalidValidation = BMRCalculator.validateBMRResult(invalidBMR, "male", 25, 70.0);
        assertTrue("无效BMR应该被检测出", invalidValidation.contains("偏低"));
    }
    
    @Test
    public void testFormulaDescription() {
        // 测试公式描述
        String maleFormula = BMRCalculator.getBMRFormulaDescription("male");
        assertTrue("男性公式描述应该包含正确信息", maleFormula.contains("66") && maleFormula.contains("13.7"));
        
        String femaleFormula = BMRCalculator.getBMRFormulaDescription("female");
        assertTrue("女性公式描述应该包含正确信息", femaleFormula.contains("655") && femaleFormula.contains("9.6"));
        
        String invalidFormula = BMRCalculator.getBMRFormulaDescription("unknown");
        assertTrue("无效性别应该返回错误信息", invalidFormula.contains("无效"));
    }
    
    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // 最小有效值
        double minBMR = BMRCalculator.calculateBMR("female", 18, 40.0, 140.0);
        assertTrue("最小有效值应该能计算出合理的BMR", minBMR > 800 && minBMR < 1200);
        
        // 最大有效值
        double maxBMR = BMRCalculator.calculateBMR("male", 80, 120.0, 200.0);
        assertTrue("最大有效值应该能计算出合理的BMR", maxBMR > 1800 && maxBMR < 2500);
        
        // 测试不同年龄段
        double youngBMR = BMRCalculator.calculateBMR("male", 20, 70.0, 175.0);
        double oldBMR = BMRCalculator.calculateBMR("male", 60, 70.0, 175.0);
        assertTrue("年轻人的BMR应该高于老年人", youngBMR > oldBMR);
        
        // 测试不同体重
        double lightBMR = BMRCalculator.calculateBMR("male", 25, 60.0, 175.0);
        double heavyBMR = BMRCalculator.calculateBMR("male", 25, 90.0, 175.0);
        assertTrue("重的人BMR应该高于轻的人", heavyBMR > lightBMR);
        
        // 测试不同身高
        double shortBMR = BMRCalculator.calculateBMR("male", 25, 70.0, 160.0);
        double tallBMR = BMRCalculator.calculateBMR("male", 25, 70.0, 190.0);
        assertTrue("高的人BMR应该高于矮的人", tallBMR > shortBMR);
    }
    
    @Test
    public void testGenderDifferences() {
        // 测试性别差异
        double maleBMR = BMRCalculator.calculateBMR("male", 25, 70.0, 175.0);
        double femaleBMR = BMRCalculator.calculateBMR("female", 25, 70.0, 175.0);
        
        assertTrue("相同条件下男性BMR应该高于女性", maleBMR > femaleBMR);
        
        // 验证差异在合理范围内（通常男性比女性高200-400卡路里）
        double difference = maleBMR - femaleBMR;
        assertTrue("性别差异应该在合理范围内", difference >= 200 && difference <= 500);
    }
    
    @Test
    public void testCalculationConsistency() {
        // 测试计算一致性
        String gender = "male";
        int age = 30;
        double weight = 75.0;
        double height = 180.0;
        
        // 多次计算应该得到相同结果
        double bmr1 = BMRCalculator.calculateBMR(gender, age, weight, height);
        double bmr2 = BMRCalculator.calculateBMR(gender, age, weight, height);
        double bmr3 = BMRCalculator.calculateBMR(gender, age, weight, height);
        
        assertEquals("多次计算应该得到相同结果", bmr1, bmr2, 0.001);
        assertEquals("多次计算应该得到相同结果", bmr2, bmr3, 0.001);
    }
}
