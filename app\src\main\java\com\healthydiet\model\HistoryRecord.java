package com.healthydiet.model;

import java.util.Date;

/**
 * 历史记录数据模型类
 * 存储用户的体重变化、BMR计算结果和饮食建议历史
 */
public class HistoryRecord {
    private long id;
    private long userId; // 关联的用户ID
    private Date date; // 记录日期
    private double weight; // 当时的体重
    private double bmr; // 基础代谢率
    private double calorieDeficit; // 热量缺口
    private String suggestion; // 饮食建议内容
    private String activityLevel; // 运动强度级别
    private Date createdAt;

    // 构造函数
    public HistoryRecord() {
        this.date = new Date();
        this.createdAt = new Date();
    }

    public HistoryRecord(long userId, double weight, double bmr, double calorieDeficit, 
                        String suggestion, String activityLevel) {
        this();
        this.userId = userId;
        this.weight = weight;
        this.bmr = bmr;
        this.calorieDeficit = calorieDeficit;
        this.suggestion = suggestion;
        this.activityLevel = activityLevel;
    }

    // Getter和Setter方法
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public double getBmr() {
        return bmr;
    }

    public void setBmr(double bmr) {
        this.bmr = bmr;
    }

    public double getCalorieDeficit() {
        return calorieDeficit;
    }

    public void setCalorieDeficit(double calorieDeficit) {
        this.calorieDeficit = calorieDeficit;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getActivityLevel() {
        return activityLevel;
    }

    public void setActivityLevel(String activityLevel) {
        this.activityLevel = activityLevel;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 验证历史记录数据的有效性
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return userId > 0 &&
               date != null &&
               weight > 0 && weight < 500 &&
               bmr > 0 && bmr < 5000 &&
               suggestion != null && !suggestion.trim().isEmpty() &&
               activityLevel != null && !activityLevel.trim().isEmpty();
    }

    /**
     * 格式化显示日期
     * @return 格式化的日期字符串
     */
    public String getFormattedDate() {
        if (date == null) return "";
        return android.text.format.DateFormat.format("yyyy-MM-dd", date).toString();
    }

    @Override
    public String toString() {
        return "HistoryRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", date=" + date +
                ", weight=" + weight +
                ", bmr=" + bmr +
                ", calorieDeficit=" + calorieDeficit +
                ", suggestion='" + suggestion + '\'' +
                ", activityLevel='" + activityLevel + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
