<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.HealthyDiet" parent="Theme.Material3.DayNight">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.HealthyDiet.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/md_theme_light_primary</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.HealthyDiet</item>
    </style>

    <!-- Card style -->
    <style name="Widget.HealthyDiet.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Button styles -->
    <style name="Widget.HealthyDiet.Button" parent="Widget.Material3.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">8dp</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <style name="Widget.HealthyDiet.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">8dp</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- Text input styles -->
    <style name="Widget.HealthyDiet.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>

    <!-- Text styles -->
    <style name="TextAppearance.HealthyDiet.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/md_theme_light_onSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.HealthyDiet.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/md_theme_light_onSurface</item>
    </style>

    <style name="TextAppearance.HealthyDiet.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/md_theme_light_onSurfaceVariant</item>
    </style>
</resources>
