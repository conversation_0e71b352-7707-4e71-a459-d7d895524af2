package com.healthydiet.calculator;

import android.util.Log;

import com.healthydiet.utils.ValidationUtils;

/**
 * 热量缺口计算器
 * 计算总消耗热量(TDEE)和热量缺口，支持不同运动强度级别
 */
public class CalorieDeficitCalculator {
    private static final String TAG = "CalorieDeficitCalculator";
    
    // 活动系数常量
    public static final String ACTIVITY_SEDENTARY = "sedentary"; // 久坐
    public static final String ACTIVITY_LIGHT = "light"; // 轻度活动
    public static final String ACTIVITY_MODERATE = "moderate"; // 中度活动
    public static final String ACTIVITY_HEAVY = "heavy"; // 重度活动
    public static final String ACTIVITY_EXTREME = "extreme"; // 极重活动
    
    // 活动系数值
    public static final double FACTOR_SEDENTARY = 1.2; // 久坐：很少或没有运动
    public static final double FACTOR_LIGHT = 1.375; // 轻度：每周1-3次轻度运动
    public static final double FACTOR_MODERATE = 1.55; // 中度：每周3-5次中等强度运动
    public static final double FACTOR_HEAVY = 1.725; // 重度：每周6-7次高强度运动
    public static final double FACTOR_EXTREME = 1.9; // 极重：每天2次运动或体力劳动
    
    // 热量转换常量
    public static final double CALORIES_PER_KG_FAT = 7700; // 1公斤脂肪约等于7700卡路里
    public static final double SAFE_WEEKLY_WEIGHT_LOSS = 0.5; // 安全的每周减重量(kg)
    public static final double MAX_WEEKLY_WEIGHT_LOSS = 1.0; // 最大每周减重量(kg)
    public static final double SAFE_WEEKLY_WEIGHT_GAIN = 0.25; // 安全的每周增重量(kg)
    public static final double MAX_WEEKLY_WEIGHT_GAIN = 0.5; // 最大每周增重量(kg)
    
    /**
     * 根据BMR和活动水平计算总消耗热量(TDEE)
     * @param bmr 基础代谢率
     * @param activityLevel 活动水平
     * @return 总消耗热量，计算失败返回-1
     */
    public static double calculateTotalCalories(double bmr, String activityLevel) {
        if (!ValidationUtils.isValidBMR(bmr)) {
            Log.e(TAG, "无效的BMR值: " + bmr);
            return -1;
        }
        
        if (activityLevel == null || activityLevel.trim().isEmpty()) {
            Log.e(TAG, "活动水平不能为空");
            return -1;
        }
        
        double activityFactor = getActivityFactor(activityLevel);
        if (activityFactor <= 0) {
            Log.e(TAG, "无效的活动水平: " + activityLevel);
            return -1;
        }
        
        try {
            double tdee = bmr * activityFactor;
            
            // 验证结果
            if (!ValidationUtils.isValidCalories(tdee)) {
                Log.e(TAG, "TDEE计算结果超出有效范围: " + tdee);
                return -1;
            }
            
            tdee = ValidationUtils.formatDecimal(tdee, 1);
            
            Log.d(TAG, String.format("TDEE计算: BMR=%.1f × 活动系数=%.3f = %.1f kcal/day", 
                  bmr, activityFactor, tdee));
                  
            return tdee;
            
        } catch (Exception e) {
            Log.e(TAG, "TDEE计算过程中发生异常", e);
            return -1;
        }
    }
    
    /**
     * 计算热量缺口
     * @param currentWeight 当前体重 (kg)
     * @param targetWeight 目标体重 (kg)
     * @param timeFrameDays 时间框架 (天)
     * @return 每日热量缺口，正值表示需要缺口(减重)，负值表示需要盈余(增重)
     */
    public static double calculateDeficit(double currentWeight, double targetWeight, int timeFrameDays) {
        if (!ValidationUtils.isValidWeight(currentWeight)) {
            Log.e(TAG, "无效的当前体重: " + currentWeight);
            return 0;
        }
        
        if (!ValidationUtils.isValidWeight(targetWeight)) {
            Log.e(TAG, "无效的目标体重: " + targetWeight);
            return 0;
        }
        
        if (!ValidationUtils.isValidTimeFrame(timeFrameDays)) {
            Log.e(TAG, "无效的时间框架: " + timeFrameDays);
            return 0;
        }
        
        try {
            double weightDifference = currentWeight - targetWeight; // 正值=减重，负值=增重
            double totalCalorieDeficit = weightDifference * CALORIES_PER_KG_FAT;
            double dailyDeficit = totalCalorieDeficit / timeFrameDays;
            
            dailyDeficit = ValidationUtils.formatDecimal(dailyDeficit, 1);
            
            String goal = weightDifference > 0 ? "减重" : "增重";
            Log.d(TAG, String.format("%s计算: 体重差=%.1fkg, 时间=%d天, 每日热量缺口=%.1f kcal", 
                  goal, Math.abs(weightDifference), timeFrameDays, dailyDeficit));
                  
            return dailyDeficit;
            
        } catch (Exception e) {
            Log.e(TAG, "热量缺口计算过程中发生异常", e);
            return 0;
        }
    }
    
    /**
     * 计算建议的每日摄入热量
     * @param tdee 总消耗热量
     * @param calorieDeficit 热量缺口
     * @return 建议的每日摄入热量
     */
    public static double calculateRecommendedIntake(double tdee, double calorieDeficit) {
        if (!ValidationUtils.isValidCalories(tdee)) {
            Log.e(TAG, "无效的TDEE值: " + tdee);
            return -1;
        }
        
        try {
            double recommendedIntake = tdee - calorieDeficit;
            
            // 确保摄入热量不低于基础代谢率的80%（安全下限）
            double minIntake = tdee / 1.2 * 0.8; // 假设TDEE是BMR的1.2倍以上
            if (recommendedIntake < minIntake) {
                Log.w(TAG, String.format("建议摄入热量过低，调整为安全下限: %.1f -> %.1f", 
                      recommendedIntake, minIntake));
                recommendedIntake = minIntake;
            }
            
            // 确保摄入热量不超过TDEE的150%（合理上限）
            double maxIntake = tdee * 1.5;
            if (recommendedIntake > maxIntake) {
                Log.w(TAG, String.format("建议摄入热量过高，调整为合理上限: %.1f -> %.1f", 
                      recommendedIntake, maxIntake));
                recommendedIntake = maxIntake;
            }
            
            recommendedIntake = ValidationUtils.formatDecimal(recommendedIntake, 1);
            
            Log.d(TAG, String.format("建议摄入热量: TDEE=%.1f - 热量缺口=%.1f = %.1f kcal/day", 
                  tdee, calorieDeficit, recommendedIntake));
                  
            return recommendedIntake;
            
        } catch (Exception e) {
            Log.e(TAG, "建议摄入热量计算过程中发生异常", e);
            return -1;
        }
    }
    
    /**
     * 根据活动水平字符串获取活动系数
     * @param activityLevel 活动水平
     * @return 活动系数，无效返回-1
     */
    public static double getActivityFactor(String activityLevel) {
        if (activityLevel == null) {
            return -1;
        }
        
        switch (activityLevel.toLowerCase().trim()) {
            case ACTIVITY_SEDENTARY:
                return FACTOR_SEDENTARY;
            case ACTIVITY_LIGHT:
                return FACTOR_LIGHT;
            case ACTIVITY_MODERATE:
                return FACTOR_MODERATE;
            case ACTIVITY_HEAVY:
                return FACTOR_HEAVY;
            case ACTIVITY_EXTREME:
                return FACTOR_EXTREME;
            default:
                return -1;
        }
    }
    
    /**
     * 获取活动水平的描述
     * @param activityLevel 活动水平
     * @return 活动水平描述
     */
    public static String getActivityDescription(String activityLevel) {
        if (activityLevel == null) {
            return "未知活动水平";
        }
        
        switch (activityLevel.toLowerCase().trim()) {
            case ACTIVITY_SEDENTARY:
                return "久坐 (很少或没有运动)";
            case ACTIVITY_LIGHT:
                return "轻度活动 (每周1-3次轻度运动)";
            case ACTIVITY_MODERATE:
                return "中度活动 (每周3-5次中等强度运动)";
            case ACTIVITY_HEAVY:
                return "重度活动 (每周6-7次高强度运动)";
            case ACTIVITY_EXTREME:
                return "极重活动 (每天2次运动或体力劳动)";
            default:
                return "未知活动水平";
        }
    }
    
    /**
     * 获取所有可用的活动水平
     * @return 活动水平数组
     */
    public static String[] getAllActivityLevels() {
        return new String[]{
            ACTIVITY_SEDENTARY,
            ACTIVITY_LIGHT,
            ACTIVITY_MODERATE,
            ACTIVITY_HEAVY,
            ACTIVITY_EXTREME
        };
    }
    
    /**
     * 获取所有活动系数
     * @return 活动系数数组
     */
    public static double[] getAllActivityFactors() {
        return new double[]{
            FACTOR_SEDENTARY,
            FACTOR_LIGHT,
            FACTOR_MODERATE,
            FACTOR_HEAVY,
            FACTOR_EXTREME
        };
    }
    
    /**
     * 计算达到目标体重所需的时间
     * @param currentWeight 当前体重
     * @param targetWeight 目标体重
     * @param dailyDeficit 每日热量缺口
     * @return 所需天数，计算失败返回-1
     */
    public static int calculateTimeToGoal(double currentWeight, double targetWeight, double dailyDeficit) {
        if (!ValidationUtils.isValidWeight(currentWeight) || !ValidationUtils.isValidWeight(targetWeight)) {
            Log.e(TAG, "体重参数无效");
            return -1;
        }
        
        if (Math.abs(dailyDeficit) < 50) { // 每日热量缺口太小
            Log.e(TAG, "每日热量缺口太小，无法有效计算时间");
            return -1;
        }
        
        try {
            double weightDifference = Math.abs(currentWeight - targetWeight);
            if (weightDifference < 0.1) { // 已经接近目标体重
                return 0;
            }
            
            double totalCaloriesNeeded = weightDifference * CALORIES_PER_KG_FAT;
            int daysNeeded = (int) Math.ceil(totalCaloriesNeeded / Math.abs(dailyDeficit));
            
            Log.d(TAG, String.format("达到目标时间计算: 体重差=%.1fkg, 每日缺口=%.1f, 需要%d天", 
                  weightDifference, dailyDeficit, daysNeeded));
                  
            return daysNeeded;
            
        } catch (Exception e) {
            Log.e(TAG, "目标时间计算过程中发生异常", e);
            return -1;
        }
    }
    
    /**
     * 验证减重/增重计划的安全性
     * @param currentWeight 当前体重
     * @param targetWeight 目标体重
     * @param timeFrameDays 时间框架
     * @return 安全性评估结果
     */
    public static SafetyAssessment assessWeightChangeSafety(double currentWeight, double targetWeight, int timeFrameDays) {
        SafetyAssessment assessment = new SafetyAssessment();
        
        if (!ValidationUtils.isValidWeight(currentWeight) || !ValidationUtils.isValidWeight(targetWeight) || 
            !ValidationUtils.isValidTimeFrame(timeFrameDays)) {
            assessment.setSafe(false);
            assessment.addWarning("输入参数无效");
            return assessment;
        }
        
        double weightDifference = currentWeight - targetWeight;
        double weeklyChange = (weightDifference * 7.0) / timeFrameDays;
        
        if (weightDifference > 0) { // 减重
            if (weeklyChange <= SAFE_WEEKLY_WEIGHT_LOSS) {
                assessment.setSafe(true);
                assessment.setLevel("安全");
                assessment.addRecommendation("减重速度适中，有利于长期维持");
            } else if (weeklyChange <= MAX_WEEKLY_WEIGHT_LOSS) {
                assessment.setSafe(true);
                assessment.setLevel("可接受");
                assessment.addWarning("减重速度较快，需要密切监控");
                assessment.addRecommendation("建议增加蛋白质摄入，保持肌肉量");
            } else {
                assessment.setSafe(false);
                assessment.setLevel("危险");
                assessment.addWarning("减重速度过快，可能导致肌肉流失和营养不良");
                assessment.addRecommendation("建议延长时间框架或降低目标体重");
            }
        } else if (weightDifference < 0) { // 增重
            double absWeeklyChange = Math.abs(weeklyChange);
            if (absWeeklyChange <= SAFE_WEEKLY_WEIGHT_GAIN) {
                assessment.setSafe(true);
                assessment.setLevel("安全");
                assessment.addRecommendation("增重速度适中，有利于健康增重");
            } else if (absWeeklyChange <= MAX_WEEKLY_WEIGHT_GAIN) {
                assessment.setSafe(true);
                assessment.setLevel("可接受");
                assessment.addWarning("增重速度较快，注意脂肪堆积");
                assessment.addRecommendation("建议结合力量训练，增加肌肉比例");
            } else {
                assessment.setSafe(false);
                assessment.setLevel("危险");
                assessment.addWarning("增重速度过快，可能导致过多脂肪堆积");
                assessment.addRecommendation("建议延长时间框架或降低目标体重");
            }
        } else { // 维持体重
            assessment.setSafe(true);
            assessment.setLevel("维持");
            assessment.addRecommendation("保持当前体重，注意营养均衡");
        }
        
        return assessment;
    }
    
    /**
     * 计算完整的热量计划
     * @param bmr 基础代谢率
     * @param activityLevel 活动水平
     * @param currentWeight 当前体重
     * @param targetWeight 目标体重
     * @param timeFrameDays 时间框架
     * @return 完整的热量计划
     */
    public static CaloriePlan calculateCompletePlan(double bmr, String activityLevel, 
                                                   double currentWeight, double targetWeight, int timeFrameDays) {
        CaloriePlan plan = new CaloriePlan();
        
        try {
            // 计算TDEE
            double tdee = calculateTotalCalories(bmr, activityLevel);
            if (tdee <= 0) {
                plan.setValid(false);
                plan.setErrorMessage("TDEE计算失败");
                return plan;
            }
            
            // 计算热量缺口
            double deficit = calculateDeficit(currentWeight, targetWeight, timeFrameDays);
            
            // 计算建议摄入
            double recommendedIntake = calculateRecommendedIntake(tdee, deficit);
            if (recommendedIntake <= 0) {
                plan.setValid(false);
                plan.setErrorMessage("建议摄入热量计算失败");
                return plan;
            }
            
            // 计算达到目标所需时间
            int timeToGoal = calculateTimeToGoal(currentWeight, targetWeight, deficit);
            
            // 安全性评估
            SafetyAssessment safety = assessWeightChangeSafety(currentWeight, targetWeight, timeFrameDays);
            
            // 设置计划数据
            plan.setValid(true);
            plan.setBmr(bmr);
            plan.setTdee(tdee);
            plan.setActivityLevel(activityLevel);
            plan.setActivityFactor(getActivityFactor(activityLevel));
            plan.setCalorieDeficit(deficit);
            plan.setRecommendedIntake(recommendedIntake);
            plan.setCurrentWeight(currentWeight);
            plan.setTargetWeight(targetWeight);
            plan.setTimeFrameDays(timeFrameDays);
            plan.setTimeToGoalDays(timeToGoal);
            plan.setSafetyAssessment(safety);
            
            Log.d(TAG, "完整热量计划计算成功: " + plan.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "完整热量计划计算过程中发生异常", e);
            plan.setValid(false);
            plan.setErrorMessage("计算过程中发生异常: " + e.getMessage());
        }
        
        return plan;
    }
    
    /**
     * 安全性评估结果类
     */
    public static class SafetyAssessment {
        private boolean safe = true;
        private String level = "";
        private StringBuilder warnings = new StringBuilder();
        private StringBuilder recommendations = new StringBuilder();
        
        public boolean isSafe() { return safe; }
        public void setSafe(boolean safe) { this.safe = safe; }
        
        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }
        
        public String getWarnings() { return warnings.toString(); }
        public void addWarning(String warning) {
            if (warnings.length() > 0) warnings.append("; ");
            warnings.append(warning);
        }
        
        public String getRecommendations() { return recommendations.toString(); }
        public void addRecommendation(String recommendation) {
            if (recommendations.length() > 0) recommendations.append("; ");
            recommendations.append(recommendation);
        }
    }
    
    /**
     * 完整热量计划类
     */
    public static class CaloriePlan {
        private boolean valid = false;
        private String errorMessage = "";
        private double bmr;
        private double tdee;
        private String activityLevel;
        private double activityFactor;
        private double calorieDeficit;
        private double recommendedIntake;
        private double currentWeight;
        private double targetWeight;
        private int timeFrameDays;
        private int timeToGoalDays;
        private SafetyAssessment safetyAssessment;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public double getBmr() { return bmr; }
        public void setBmr(double bmr) { this.bmr = bmr; }
        
        public double getTdee() { return tdee; }
        public void setTdee(double tdee) { this.tdee = tdee; }
        
        public String getActivityLevel() { return activityLevel; }
        public void setActivityLevel(String activityLevel) { this.activityLevel = activityLevel; }
        
        public double getActivityFactor() { return activityFactor; }
        public void setActivityFactor(double activityFactor) { this.activityFactor = activityFactor; }
        
        public double getCalorieDeficit() { return calorieDeficit; }
        public void setCalorieDeficit(double calorieDeficit) { this.calorieDeficit = calorieDeficit; }
        
        public double getRecommendedIntake() { return recommendedIntake; }
        public void setRecommendedIntake(double recommendedIntake) { this.recommendedIntake = recommendedIntake; }
        
        public double getCurrentWeight() { return currentWeight; }
        public void setCurrentWeight(double currentWeight) { this.currentWeight = currentWeight; }
        
        public double getTargetWeight() { return targetWeight; }
        public void setTargetWeight(double targetWeight) { this.targetWeight = targetWeight; }
        
        public int getTimeFrameDays() { return timeFrameDays; }
        public void setTimeFrameDays(int timeFrameDays) { this.timeFrameDays = timeFrameDays; }
        
        public int getTimeToGoalDays() { return timeToGoalDays; }
        public void setTimeToGoalDays(int timeToGoalDays) { this.timeToGoalDays = timeToGoalDays; }
        
        public SafetyAssessment getSafetyAssessment() { return safetyAssessment; }
        public void setSafetyAssessment(SafetyAssessment safetyAssessment) { this.safetyAssessment = safetyAssessment; }
        
        @Override
        public String toString() {
            return String.format("CaloriePlan{valid=%s, BMR=%.1f, TDEE=%.1f, deficit=%.1f, intake=%.1f, safety=%s}", 
                               valid, bmr, tdee, calorieDeficit, recommendedIntake, 
                               safetyAssessment != null ? safetyAssessment.getLevel() : "未评估");
        }
    }
}
