package com.healthydiet.calculator;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * 营养计算器单元测试
 * 验证营养素分配计算的准确性
 */
public class NutritionCalculatorTest {
    
    private static final double DELTA = 0.1; // 允许的误差范围
    
    @Test
    public void testMacronutrientCalculation() {
        // 测试营养素分配计算
        double totalCalories = 2000.0;
        double proteinPercentage = 0.25; // 25%
        double carbPercentage = 0.50; // 50%
        double fatPercentage = 0.25; // 25%
        
        NutritionCalculator.MacronutrientDistribution distribution = 
            NutritionCalculator.calculateMacronutrients(totalCalories, proteinPercentage, carbPercentage, fatPercentage);
        
        assertTrue("营养素分配应该有效", distribution.isValid());
        assertEquals("总热量应该正确", totalCalories, distribution.getTotalCalories(), DELTA);
        
        // 验证蛋白质计算
        assertEquals("蛋白质比例应该正确", proteinPercentage, distribution.getProteinPercentage(), 0.001);
        assertEquals("蛋白质热量应该正确", 500.0, distribution.getProteinCalories(), DELTA);
        assertEquals("蛋白质克数应该正确", 125.0, distribution.getProteinGrams(), DELTA); // 500/4
        
        // 验证碳水化合物计算
        assertEquals("碳水比例应该正确", carbPercentage, distribution.getCarbPercentage(), 0.001);
        assertEquals("碳水热量应该正确", 1000.0, distribution.getCarbCalories(), DELTA);
        assertEquals("碳水克数应该正确", 250.0, distribution.getCarbGrams(), DELTA); // 1000/4
        
        // 验证脂肪计算
        assertEquals("脂肪比例应该正确", fatPercentage, distribution.getFatPercentage(), 0.001);
        assertEquals("脂肪热量应该正确", 500.0, distribution.getFatCalories(), DELTA);
        assertEquals("脂肪克数应该正确", 55.6, distribution.getFatGrams(), DELTA); // 500/9
    }
    
    @Test
    public void testOptimalMacronutrientCalculation() {
        // 测试最佳营养素分配计算
        double totalCalories = 1800.0;
        String goal = "weight_loss";
        String activityLevel = CalorieDeficitCalculator.ACTIVITY_MODERATE;
        int age = 30;
        double weight = 70.0;
        
        NutritionCalculator.MacronutrientDistribution distribution = 
            NutritionCalculator.calculateOptimalMacronutrients(totalCalories, goal, activityLevel, age, weight);
        
        assertTrue("最佳营养素分配应该有效", distribution.isValid());
        assertEquals("总热量应该正确", totalCalories, distribution.getTotalCalories(), DELTA);
        
        // 减重期应该有较高的蛋白质比例
        assertTrue("减重期蛋白质比例应该较高", distribution.getProteinPercentage() >= 0.25);
        
        // 验证比例总和为100%
        double totalPercentage = distribution.getProteinPercentage() + 
                               distribution.getCarbPercentage() + 
                               distribution.getFatPercentage();
        assertEquals("营养素比例总和应该为100%", 1.0, totalPercentage, 0.01);
    }
    
    @Test
    public void testMinProteinRequirement() {
        // 测试最低蛋白质需求计算
        double weight = 70.0;
        int age = 25;
        
        // 久坐人群
        double sedentaryProtein = NutritionCalculator.calculateMinProteinRequirement(
            weight, CalorieDeficitCalculator.ACTIVITY_SEDENTARY, age);
        assertEquals("久坐人群蛋白质需求", 56.0, sedentaryProtein, DELTA); // 70 * 0.8
        
        // 活跃人群
        double activeProtein = NutritionCalculator.calculateMinProteinRequirement(
            weight, CalorieDeficitCalculator.ACTIVITY_MODERATE, age);
        assertEquals("活跃人群蛋白质需求", 84.0, activeProtein, DELTA); // 70 * 1.2
        
        // 运动员
        double athleteProtein = NutritionCalculator.calculateMinProteinRequirement(
            weight, CalorieDeficitCalculator.ACTIVITY_EXTREME, age);
        assertEquals("运动员蛋白质需求", 112.0, athleteProtein, DELTA); // 70 * 1.6
        
        // 老年人
        double elderlyProtein = NutritionCalculator.calculateMinProteinRequirement(
            weight, CalorieDeficitCalculator.ACTIVITY_SEDENTARY, 70);
        assertEquals("老年人蛋白质需求", 70.0, elderlyProtein, DELTA); // 70 * 1.0
    }
    
    @Test
    public void testMacroPercentageValidation() {
        // 测试营养素比例验证
        
        // 有效比例
        assertTrue("有效比例应该通过验证", 
                  NutritionCalculator.isValidMacroPercentages(0.25, 0.50, 0.25));
        
        // 总和不等于100%
        assertFalse("总和不等于100%应该无效", 
                   NutritionCalculator.isValidMacroPercentages(0.30, 0.50, 0.30));
        
        // 负值
        assertFalse("负值应该无效", 
                   NutritionCalculator.isValidMacroPercentages(-0.1, 0.60, 0.50));
        
        // 超过100%
        assertFalse("超过100%应该无效", 
                   NutritionCalculator.isValidMacroPercentages(1.5, 0.30, 0.20));
    }
    
    @Test
    public void testMacroPercentageValidationWithWarnings() {
        // 测试营养素比例验证（包含警告）
        
        // 正常比例
        NutritionCalculator.NutritionValidationResult normalResult = 
            NutritionCalculator.validateMacroPercentages(0.25, 0.50, 0.25);
        assertTrue("正常比例应该有效", normalResult.isValid());
        assertFalse("正常比例不应该有警告", normalResult.hasWarnings());
        
        // 蛋白质过低
        NutritionCalculator.NutritionValidationResult lowProteinResult = 
            NutritionCalculator.validateMacroPercentages(0.10, 0.60, 0.30);
        assertTrue("低蛋白质比例应该有效但有警告", lowProteinResult.isValid());
        assertTrue("低蛋白质应该有警告", lowProteinResult.hasWarnings());
        assertTrue("警告应该包含蛋白质", lowProteinResult.getWarnings().contains("蛋白质"));
        
        // 脂肪过高
        NutritionCalculator.NutritionValidationResult highFatResult = 
            NutritionCalculator.validateMacroPercentages(0.20, 0.40, 0.40);
        assertTrue("高脂肪比例应该有效但有警告", highFatResult.isValid());
        assertTrue("高脂肪应该有警告", highFatResult.hasWarnings());
        assertTrue("警告应该包含脂肪", highFatResult.getWarnings().contains("脂肪"));
    }
    
    @Test
    public void testTotalCaloriesCalculation() {
        // 测试总热量计算
        double proteinGrams = 100.0;
        double carbGrams = 200.0;
        double fatGrams = 50.0;
        
        double totalCalories = NutritionCalculator.calculateTotalCalories(proteinGrams, carbGrams, fatGrams);
        
        // 手动计算：100*4 + 200*4 + 50*9 = 400 + 800 + 450 = 1650
        assertEquals("总热量计算应该正确", 1650.0, totalCalories, DELTA);
    }
    
    @Test
    public void testMacroPercentagesCalculation() {
        // 测试营养素比例计算
        double proteinGrams = 125.0; // 500 kcal
        double carbGrams = 250.0; // 1000 kcal
        double fatGrams = 55.6; // 500 kcal
        // 总计：2000 kcal
        
        double[] percentages = NutritionCalculator.calculateMacroPercentages(proteinGrams, carbGrams, fatGrams);
        
        assertEquals("应该返回3个比例", 3, percentages.length);
        assertEquals("蛋白质比例应该正确", 0.25, percentages[0], 0.01);
        assertEquals("碳水比例应该正确", 0.50, percentages[1], 0.01);
        assertEquals("脂肪比例应该正确", 0.25, percentages[2], 0.01);
    }
    
    @Test
    public void testInvalidInputs() {
        // 测试无效输入
        
        // 无效总热量
        NutritionCalculator.MacronutrientDistribution invalidCaloriesResult = 
            NutritionCalculator.calculateMacronutrients(-100.0, 0.25, 0.50, 0.25);
        assertFalse("无效热量应该返回无效结果", invalidCaloriesResult.isValid());
        
        // 无效比例
        NutritionCalculator.MacronutrientDistribution invalidPercentageResult = 
            NutritionCalculator.calculateMacronutrients(2000.0, 0.30, 0.50, 0.30);
        assertFalse("无效比例应该返回无效结果", invalidPercentageResult.isValid());
        
        // 无效蛋白质需求计算
        double invalidProtein = NutritionCalculator.calculateMinProteinRequirement(-10.0, "moderate", 25);
        assertEquals("无效体重应该返回0", 0.0, invalidProtein, DELTA);
    }
    
    @Test
    public void testSpecialPopulations() {
        // 测试特殊人群的营养需求
        
        // 老年人（65岁以上）
        NutritionCalculator.MacronutrientDistribution elderlyDistribution = 
            NutritionCalculator.calculateOptimalMacronutrients(1600.0, "maintain", "light", 70, 60.0);
        assertTrue("老年人营养分配应该有效", elderlyDistribution.isValid());
        assertTrue("老年人蛋白质比例应该较高", elderlyDistribution.getProteinPercentage() >= 0.25);
        
        // 运动员
        NutritionCalculator.MacronutrientDistribution athleteDistribution = 
            NutritionCalculator.calculateOptimalMacronutrients(2500.0, "muscle_gain", "extreme", 25, 80.0);
        assertTrue("运动员营养分配应该有效", athleteDistribution.isValid());
        assertTrue("运动员蛋白质比例应该很高", athleteDistribution.getProteinPercentage() >= 0.28);
        
        // 减重人群
        NutritionCalculator.MacronutrientDistribution weightLossDistribution = 
            NutritionCalculator.calculateOptimalMacronutrients(1400.0, "weight_loss", "moderate", 35, 75.0);
        assertTrue("减重人群营养分配应该有效", weightLossDistribution.isValid());
        assertTrue("减重人群蛋白质比例应该高", weightLossDistribution.getProteinPercentage() >= 0.25);
    }
    
    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // 极低热量
        NutritionCalculator.MacronutrientDistribution lowCalorieDistribution = 
            NutritionCalculator.calculateMacronutrients(800.0, 0.30, 0.45, 0.25);
        assertTrue("极低热量分配应该有效", lowCalorieDistribution.isValid());
        
        // 极高热量
        NutritionCalculator.MacronutrientDistribution highCalorieDistribution = 
            NutritionCalculator.calculateMacronutrients(4000.0, 0.20, 0.55, 0.25);
        assertTrue("极高热量分配应该有效", highCalorieDistribution.isValid());
        
        // 极端比例（但总和为100%）
        NutritionCalculator.MacronutrientDistribution extremeDistribution = 
            NutritionCalculator.calculateMacronutrients(2000.0, 0.15, 0.65, 0.20);
        assertTrue("极端比例分配应该有效", extremeDistribution.isValid());
        
        // 验证极端比例会产生警告
        NutritionCalculator.NutritionValidationResult extremeValidation = 
            NutritionCalculator.validateMacroPercentages(0.15, 0.65, 0.20);
        assertTrue("极端比例应该有警告", extremeValidation.hasWarnings());
    }
    
    @Test
    public void testCalculationConsistency() {
        // 测试计算一致性
        double totalCalories = 2200.0;
        double proteinPercentage = 0.22;
        double carbPercentage = 0.53;
        double fatPercentage = 0.25;
        
        // 多次计算应该得到相同结果
        NutritionCalculator.MacronutrientDistribution result1 = 
            NutritionCalculator.calculateMacronutrients(totalCalories, proteinPercentage, carbPercentage, fatPercentage);
        NutritionCalculator.MacronutrientDistribution result2 = 
            NutritionCalculator.calculateMacronutrients(totalCalories, proteinPercentage, carbPercentage, fatPercentage);
        
        assertTrue("两次计算都应该有效", result1.isValid() && result2.isValid());
        assertEquals("蛋白质克数应该一致", result1.getProteinGrams(), result2.getProteinGrams(), 0.001);
        assertEquals("碳水克数应该一致", result1.getCarbGrams(), result2.getCarbGrams(), 0.001);
        assertEquals("脂肪克数应该一致", result1.getFatGrams(), result2.getFatGrams(), 0.001);
    }
    
    @Test
    public void testNutritionConstants() {
        // 测试营养常量
        assertEquals("蛋白质热量常量", 4.0, NutritionCalculator.PROTEIN_CALORIES_PER_GRAM, 0.001);
        assertEquals("碳水热量常量", 4.0, NutritionCalculator.CARB_CALORIES_PER_GRAM, 0.001);
        assertEquals("脂肪热量常量", 9.0, NutritionCalculator.FAT_CALORIES_PER_GRAM, 0.001);
        assertEquals("酒精热量常量", 7.0, NutritionCalculator.ALCOHOL_CALORIES_PER_GRAM, 0.001);
        
        // 验证推荐比例范围
        assertTrue("最佳蛋白质比例应该在合理范围内", 
                  NutritionCalculator.OPTIMAL_PROTEIN_PERCENTAGE >= NutritionCalculator.MIN_PROTEIN_PERCENTAGE &&
                  NutritionCalculator.OPTIMAL_PROTEIN_PERCENTAGE <= NutritionCalculator.MAX_PROTEIN_PERCENTAGE);
        
        assertTrue("最佳碳水比例应该在合理范围内", 
                  NutritionCalculator.OPTIMAL_CARB_PERCENTAGE >= NutritionCalculator.MIN_CARB_PERCENTAGE &&
                  NutritionCalculator.OPTIMAL_CARB_PERCENTAGE <= NutritionCalculator.MAX_CARB_PERCENTAGE);
        
        assertTrue("最佳脂肪比例应该在合理范围内", 
                  NutritionCalculator.OPTIMAL_FAT_PERCENTAGE >= NutritionCalculator.MIN_FAT_PERCENTAGE &&
                  NutritionCalculator.OPTIMAL_FAT_PERCENTAGE <= NutritionCalculator.MAX_FAT_PERCENTAGE);
    }
}
