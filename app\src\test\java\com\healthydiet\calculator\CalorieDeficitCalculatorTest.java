package com.healthydiet.calculator;

import static org.junit.Assert.*;

import org.junit.Test;

/**
 * 热量缺口计算器单元测试
 * 验证TDEE计算和热量缺口计算的准确性
 */
public class CalorieDeficitCalculatorTest {
    
    private static final double DELTA = 0.1; // 允许的误差范围
    
    @Test
    public void testTDEECalculation() {
        // 测试TDEE计算
        double bmr = 1500.0;
        
        // 测试不同活动水平
        double sedentaryTDEE = CalorieDeficitCalculator.calculateTotalCalories(bmr, CalorieDeficitCalculator.ACTIVITY_SEDENTARY);
        assertEquals("久坐TDEE计算", bmr * 1.2, sedentaryTDEE, DELTA);
        
        double lightTDEE = CalorieDeficitCalculator.calculateTotalCalories(bmr, CalorieDeficitCalculator.ACTIVITY_LIGHT);
        assertEquals("轻度活动TDEE计算", bmr * 1.375, lightTDEE, DELTA);
        
        double moderateTDEE = CalorieDeficitCalculator.calculateTotalCalories(bmr, CalorieDeficitCalculator.ACTIVITY_MODERATE);
        assertEquals("中度活动TDEE计算", bmr * 1.55, moderateTDEE, DELTA);
        
        double heavyTDEE = CalorieDeficitCalculator.calculateTotalCalories(bmr, CalorieDeficitCalculator.ACTIVITY_HEAVY);
        assertEquals("重度活动TDEE计算", bmr * 1.725, heavyTDEE, DELTA);
        
        double extremeTDEE = CalorieDeficitCalculator.calculateTotalCalories(bmr, CalorieDeficitCalculator.ACTIVITY_EXTREME);
        assertEquals("极重活动TDEE计算", bmr * 1.9, extremeTDEE, DELTA);
    }
    
    @Test
    public void testActivityFactors() {
        // 测试活动系数获取
        assertEquals("久坐活动系数", 1.2, CalorieDeficitCalculator.getActivityFactor(CalorieDeficitCalculator.ACTIVITY_SEDENTARY), DELTA);
        assertEquals("轻度活动系数", 1.375, CalorieDeficitCalculator.getActivityFactor(CalorieDeficitCalculator.ACTIVITY_LIGHT), DELTA);
        assertEquals("中度活动系数", 1.55, CalorieDeficitCalculator.getActivityFactor(CalorieDeficitCalculator.ACTIVITY_MODERATE), DELTA);
        assertEquals("重度活动系数", 1.725, CalorieDeficitCalculator.getActivityFactor(CalorieDeficitCalculator.ACTIVITY_HEAVY), DELTA);
        assertEquals("极重活动系数", 1.9, CalorieDeficitCalculator.getActivityFactor(CalorieDeficitCalculator.ACTIVITY_EXTREME), DELTA);
        
        // 测试无效活动水平
        assertEquals("无效活动水平应该返回-1", -1.0, CalorieDeficitCalculator.getActivityFactor("invalid"), DELTA);
        assertEquals("空活动水平应该返回-1", -1.0, CalorieDeficitCalculator.getActivityFactor(null), DELTA);
    }
    
    @Test
    public void testCalorieDeficitCalculation() {
        // 测试热量缺口计算
        
        // 减重测试：从70kg减到65kg，30天
        double weightLossDeficit = CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 30);
        // 预期：5kg × 7700 kcal/kg ÷ 30天 = 1283.3 kcal/day
        assertEquals("减重热量缺口计算", 1283.3, weightLossDeficit, DELTA);
        
        // 增重测试：从60kg增到65kg，60天
        double weightGainDeficit = CalorieDeficitCalculator.calculateDeficit(60.0, 65.0, 60);
        // 预期：-5kg × 7700 kcal/kg ÷ 60天 = -641.7 kcal/day（负值表示需要盈余）
        assertEquals("增重热量缺口计算", -641.7, weightGainDeficit, DELTA);
        
        // 维持体重测试
        double maintainDeficit = CalorieDeficitCalculator.calculateDeficit(70.0, 70.0, 30);
        assertEquals("维持体重热量缺口应该为0", 0.0, maintainDeficit, DELTA);
    }
    
    @Test
    public void testRecommendedIntakeCalculation() {
        // 测试建议摄入热量计算
        double tdee = 2000.0;
        double deficit = 500.0; // 每日500卡路里缺口
        
        double recommendedIntake = CalorieDeficitCalculator.calculateRecommendedIntake(tdee, deficit);
        assertEquals("建议摄入热量计算", 1500.0, recommendedIntake, DELTA);
        
        // 测试过低摄入的安全限制
        double extremeDeficit = 1500.0; // 极端缺口
        double safeIntake = CalorieDeficitCalculator.calculateRecommendedIntake(tdee, extremeDeficit);
        assertTrue("摄入热量不应过低", safeIntake >= tdee / 1.2 * 0.8);
        
        // 测试过高摄入的限制
        double negativeSurplus = -1000.0; // 大量盈余
        double limitedIntake = CalorieDeficitCalculator.calculateRecommendedIntake(tdee, negativeSurplus);
        assertTrue("摄入热量不应过高", limitedIntake <= tdee * 1.5);
    }
    
    @Test
    public void testTimeToGoalCalculation() {
        // 测试达到目标所需时间计算
        
        // 减重5kg，每日500卡路里缺口
        int daysToLose = CalorieDeficitCalculator.calculateTimeToGoal(70.0, 65.0, 500.0);
        // 预期：5kg × 7700 kcal/kg ÷ 500 kcal/day = 77天
        assertEquals("减重所需时间计算", 77, daysToLose);
        
        // 增重3kg，每日300卡路里盈余
        int daysToGain = CalorieDeficitCalculator.calculateTimeToGoal(60.0, 63.0, -300.0);
        // 预期：3kg × 7700 kcal/kg ÷ 300 kcal/day = 77天
        assertEquals("增重所需时间计算", 77, daysToGain);
        
        // 已达到目标
        int daysAtGoal = CalorieDeficitCalculator.calculateTimeToGoal(70.0, 70.0, 500.0);
        assertEquals("已达到目标应该返回0", 0, daysAtGoal);
    }
    
    @Test
    public void testSafetyAssessment() {
        // 测试安全性评估
        
        // 安全减重：5kg在10周内
        CalorieDeficitCalculator.SafetyAssessment safeWeightLoss = 
            CalorieDeficitCalculator.assessWeightChangeSafety(70.0, 65.0, 70);
        assertTrue("安全减重应该被评为安全", safeWeightLoss.isSafe());
        assertEquals("安全减重等级", "安全", safeWeightLoss.getLevel());
        
        // 快速减重：10kg在4周内
        CalorieDeficitCalculator.SafetyAssessment fastWeightLoss = 
            CalorieDeficitCalculator.assessWeightChangeSafety(80.0, 70.0, 28);
        assertFalse("快速减重应该被评为不安全", fastWeightLoss.isSafe());
        assertEquals("快速减重等级", "危险", fastWeightLoss.getLevel());
        
        // 维持体重
        CalorieDeficitCalculator.SafetyAssessment maintain = 
            CalorieDeficitCalculator.assessWeightChangeSafety(70.0, 70.0, 30);
        assertTrue("维持体重应该安全", maintain.isSafe());
        assertEquals("维持体重等级", "维持", maintain.getLevel());
        
        // 安全增重
        CalorieDeficitCalculator.SafetyAssessment safeWeightGain = 
            CalorieDeficitCalculator.assessWeightChangeSafety(60.0, 63.0, 84); // 12周增重3kg
        assertTrue("安全增重应该被评为安全", safeWeightGain.isSafe());
        assertEquals("安全增重等级", "安全", safeWeightGain.getLevel());
    }
    
    @Test
    public void testCompletePlanCalculation() {
        // 测试完整计划计算
        double bmr = 1500.0;
        String activityLevel = CalorieDeficitCalculator.ACTIVITY_MODERATE;
        double currentWeight = 70.0;
        double targetWeight = 65.0;
        int timeFrame = 60; // 60天
        
        CalorieDeficitCalculator.CaloriePlan plan = CalorieDeficitCalculator.calculateCompletePlan(
            bmr, activityLevel, currentWeight, targetWeight, timeFrame);
        
        assertTrue("完整计划应该有效", plan.isValid());
        assertEquals("BMR应该正确", bmr, plan.getBmr(), DELTA);
        assertEquals("TDEE应该正确", bmr * 1.55, plan.getTdee(), DELTA);
        assertEquals("活动水平应该正确", activityLevel, plan.getActivityLevel());
        assertEquals("当前体重应该正确", currentWeight, plan.getCurrentWeight(), DELTA);
        assertEquals("目标体重应该正确", targetWeight, plan.getTargetWeight(), DELTA);
        assertEquals("时间框架应该正确", timeFrame, plan.getTimeFrameDays());
        
        // 验证热量缺口计算
        double expectedDeficit = 5.0 * 7700.0 / 60.0; // 5kg在60天内
        assertEquals("热量缺口应该正确", expectedDeficit, plan.getCalorieDeficit(), DELTA);
        
        // 验证建议摄入
        double expectedIntake = plan.getTdee() - plan.getCalorieDeficit();
        assertEquals("建议摄入应该正确", expectedIntake, plan.getRecommendedIntake(), DELTA);
        
        assertNotNull("安全性评估不应该为空", plan.getSafetyAssessment());
    }
    
    @Test
    public void testActivityDescriptions() {
        // 测试活动描述
        String sedentaryDesc = CalorieDeficitCalculator.getActivityDescription(CalorieDeficitCalculator.ACTIVITY_SEDENTARY);
        assertTrue("久坐描述应该包含相关信息", sedentaryDesc.contains("久坐"));
        
        String lightDesc = CalorieDeficitCalculator.getActivityDescription(CalorieDeficitCalculator.ACTIVITY_LIGHT);
        assertTrue("轻度活动描述应该包含相关信息", lightDesc.contains("轻度"));
        
        String moderateDesc = CalorieDeficitCalculator.getActivityDescription(CalorieDeficitCalculator.ACTIVITY_MODERATE);
        assertTrue("中度活动描述应该包含相关信息", moderateDesc.contains("中度"));
        
        String heavyDesc = CalorieDeficitCalculator.getActivityDescription(CalorieDeficitCalculator.ACTIVITY_HEAVY);
        assertTrue("重度活动描述应该包含相关信息", heavyDesc.contains("重度"));
        
        String extremeDesc = CalorieDeficitCalculator.getActivityDescription(CalorieDeficitCalculator.ACTIVITY_EXTREME);
        assertTrue("极重活动描述应该包含相关信息", extremeDesc.contains("极重"));
        
        String unknownDesc = CalorieDeficitCalculator.getActivityDescription("unknown");
        assertTrue("未知活动描述应该包含未知信息", unknownDesc.contains("未知"));
    }
    
    @Test
    public void testGetAllActivityLevels() {
        // 测试获取所有活动水平
        String[] levels = CalorieDeficitCalculator.getAllActivityLevels();
        assertNotNull("活动水平数组不应该为空", levels);
        assertEquals("应该有5个活动水平", 5, levels.length);
        
        double[] factors = CalorieDeficitCalculator.getAllActivityFactors();
        assertNotNull("活动系数数组不应该为空", factors);
        assertEquals("应该有5个活动系数", 5, factors.length);
        
        // 验证数组长度一致
        assertEquals("活动水平和系数数组长度应该一致", levels.length, factors.length);
        
        // 验证系数递增
        for (int i = 1; i < factors.length; i++) {
            assertTrue("活动系数应该递增", factors[i] > factors[i-1]);
        }
    }
    
    @Test
    public void testInvalidInputs() {
        // 测试无效输入
        
        // 无效BMR
        assertEquals("无效BMR应该返回-1", -1.0, 
            CalorieDeficitCalculator.calculateTotalCalories(-100.0, CalorieDeficitCalculator.ACTIVITY_MODERATE), DELTA);
        
        // 无效活动水平
        assertEquals("无效活动水平应该返回-1", -1.0, 
            CalorieDeficitCalculator.calculateTotalCalories(1500.0, "invalid"), DELTA);
        
        // 无效体重
        assertEquals("无效当前体重应该返回0", 0.0, 
            CalorieDeficitCalculator.calculateDeficit(-10.0, 65.0, 30), DELTA);
        assertEquals("无效目标体重应该返回0", 0.0, 
            CalorieDeficitCalculator.calculateDeficit(70.0, -10.0, 30), DELTA);
        
        // 无效时间框架
        assertEquals("无效时间框架应该返回0", 0.0, 
            CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 0), DELTA);
        
        // 无效建议摄入计算
        assertEquals("无效TDEE应该返回-1", -1.0, 
            CalorieDeficitCalculator.calculateRecommendedIntake(-100.0, 500.0), DELTA);
    }
    
    @Test
    public void testEdgeCases() {
        // 测试边界情况
        
        // 极小的体重差异
        double smallDeficit = CalorieDeficitCalculator.calculateDeficit(70.0, 69.9, 30);
        assertTrue("极小体重差异应该产生很小的缺口", Math.abs(smallDeficit) < 50);
        
        // 极大的体重差异
        double largeDeficit = CalorieDeficitCalculator.calculateDeficit(100.0, 60.0, 365);
        assertTrue("极大体重差异应该产生合理的缺口", largeDeficit > 500 && largeDeficit < 2000);
        
        // 极短时间框架
        double shortTimeDeficit = CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 7);
        assertTrue("极短时间框架应该产生很大的缺口", shortTimeDeficit > 5000);
        
        // 极长时间框架
        double longTimeDeficit = CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 365);
        assertTrue("极长时间框架应该产生较小的缺口", longTimeDeficit < 200);
    }
    
    @Test
    public void testCalculationConsistency() {
        // 测试计算一致性
        double bmr = 1600.0;
        String activity = CalorieDeficitCalculator.ACTIVITY_MODERATE;
        
        // 多次计算应该得到相同结果
        double tdee1 = CalorieDeficitCalculator.calculateTotalCalories(bmr, activity);
        double tdee2 = CalorieDeficitCalculator.calculateTotalCalories(bmr, activity);
        double tdee3 = CalorieDeficitCalculator.calculateTotalCalories(bmr, activity);
        
        assertEquals("多次TDEE计算应该一致", tdee1, tdee2, 0.001);
        assertEquals("多次TDEE计算应该一致", tdee2, tdee3, 0.001);
        
        // 热量缺口计算一致性
        double deficit1 = CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 60);
        double deficit2 = CalorieDeficitCalculator.calculateDeficit(70.0, 65.0, 60);
        
        assertEquals("多次缺口计算应该一致", deficit1, deficit2, 0.001);
    }
}
