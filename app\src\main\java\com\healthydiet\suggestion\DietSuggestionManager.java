package com.healthydiet.suggestion;

import android.util.Log;

import com.healthydiet.calculator.CalculatorUtils;
import com.healthydiet.calculator.NutritionCalculator;
import com.healthydiet.data.FoodDatabase;
import com.healthydiet.model.User;

import java.util.ArrayList;
import java.util.List;

/**
 * 饮食建议管理器
 * 提供高级的饮食建议接口，整合计算和建议功能
 */
public class DietSuggestionManager {
    private static final String TAG = "DietSuggestionManager";
    
    /**
     * 生成完整的饮食计划
     * @param user 用户信息
     * @param activityLevel 活动水平
     * @param timeFrameDays 时间框架（天）
     * @return 完整饮食计划
     */
    public static CompleteDietPlan generateCompleteDietPlan(User user, String activityLevel, int timeFrameDays) {
        CompleteDietPlan plan = new CompleteDietPlan();
        
        if (user == null || !user.isValid()) {
            plan.setValid(false);
            plan.setErrorMessage("用户信息无效");
            return plan;
        }
        
        try {
            // 计算完整健康数据
            CalculatorUtils.HealthCalculationResult healthResult = 
                CalculatorUtils.calculateCompleteHealthData(user, activityLevel, timeFrameDays);
            
            if (!healthResult.isValid()) {
                plan.setValid(false);
                plan.setErrorMessage("健康数据计算失败: " + healthResult.getErrorMessage());
                return plan;
            }
            
            // 生成饮食建议
            DietSuggestionEngine.DietSuggestion dietSuggestion = 
                DietSuggestionEngine.generateSuggestion(user, healthResult.getRecommendedIntake(), activityLevel);
            
            if (!dietSuggestion.isValid()) {
                plan.setValid(false);
                plan.setErrorMessage("饮食建议生成失败: " + dietSuggestion.getErrorMessage());
                return plan;
            }
            
            // 生成购物清单
            List<String> shoppingList = generateShoppingList(dietSuggestion);
            
            // 生成营养分析
            NutritionAnalysis nutritionAnalysis = analyzeNutrition(dietSuggestion);
            
            // 生成实施建议
            List<String> implementationTips = generateImplementationTips(user, dietSuggestion);
            
            // 设置完整计划
            plan.setValid(true);
            plan.setUser(user);
            plan.setHealthResult(healthResult);
            plan.setDietSuggestion(dietSuggestion);
            plan.setShoppingList(shoppingList);
            plan.setNutritionAnalysis(nutritionAnalysis);
            plan.setImplementationTips(implementationTips);
            plan.setTimeFrameDays(timeFrameDays);
            
            Log.d(TAG, "完整饮食计划生成成功: " + plan.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "生成完整饮食计划过程中发生异常", e);
            plan.setValid(false);
            plan.setErrorMessage("生成计划过程中发生异常: " + e.getMessage());
        }
        
        return plan;
    }
    
    /**
     * 生成购物清单
     * @param dietSuggestion 饮食建议
     * @return 购物清单
     */
    private static List<String> generateShoppingList(DietSuggestionEngine.DietSuggestion dietSuggestion) {
        List<String> shoppingList = new ArrayList<>();
        
        // 收集所有推荐的食物
        List<String> recommendedFoods = new ArrayList<>();
        for (DietSuggestionEngine.MealSuggestion meal : dietSuggestion.getMealSuggestions()) {
            for (FoodDatabase.FoodRecommendation food : meal.getFoodRecommendations()) {
                if (!recommendedFoods.contains(food.getFoodName())) {
                    recommendedFoods.add(food.getFoodName());
                }
            }
        }
        
        // 按分类组织购物清单
        addCategoryToShoppingList(shoppingList, "蛋白质类", recommendedFoods, FoodDatabase.CATEGORY_PROTEIN);
        addCategoryToShoppingList(shoppingList, "碳水化合物类", recommendedFoods, FoodDatabase.CATEGORY_CARB);
        addCategoryToShoppingList(shoppingList, "蔬菜类", recommendedFoods, FoodDatabase.CATEGORY_VEGETABLE);
        addCategoryToShoppingList(shoppingList, "水果类", recommendedFoods, FoodDatabase.CATEGORY_FRUIT);
        addCategoryToShoppingList(shoppingList, "乳制品类", recommendedFoods, FoodDatabase.CATEGORY_DAIRY);
        addCategoryToShoppingList(shoppingList, "坚果类", recommendedFoods, FoodDatabase.CATEGORY_NUTS);
        addCategoryToShoppingList(shoppingList, "健康脂肪类", recommendedFoods, FoodDatabase.CATEGORY_FAT);
        
        // 添加基本调料和饮品
        shoppingList.add("调料类: 橄榄油、盐、胡椒、香料");
        shoppingList.add("饮品类: 绿茶、柠檬水、无糖饮品");
        
        return shoppingList;
    }
    
    /**
     * 添加分类到购物清单
     */
    private static void addCategoryToShoppingList(List<String> shoppingList, String categoryName, 
                                                 List<String> recommendedFoods, String category) {
        List<String> categoryFoods = FoodDatabase.getFoodsByCategory(category);
        List<String> neededFoods = new ArrayList<>();
        
        for (String food : recommendedFoods) {
            if (categoryFoods.contains(food)) {
                neededFoods.add(food);
            }
        }
        
        if (!neededFoods.isEmpty()) {
            StringBuilder categoryItem = new StringBuilder(categoryName + ": ");
            for (int i = 0; i < neededFoods.size(); i++) {
                if (i > 0) categoryItem.append("、");
                categoryItem.append(neededFoods.get(i));
            }
            shoppingList.add(categoryItem.toString());
        }
    }
    
    /**
     * 分析营养状况
     * @param dietSuggestion 饮食建议
     * @return 营养分析
     */
    private static NutritionAnalysis analyzeNutrition(DietSuggestionEngine.DietSuggestion dietSuggestion) {
        NutritionAnalysis analysis = new NutritionAnalysis();
        
        NutritionCalculator.MacronutrientDistribution macros = dietSuggestion.getMacronutrients();
        
        // 分析营养素比例
        analysis.setProteinAdequacy(analyzeProteinAdequacy(macros));
        analysis.setCarbAdequacy(analyzeCarbAdequacy(macros));
        analysis.setFatAdequacy(analyzeFatAdequacy(macros));
        
        // 计算营养密度
        analysis.setNutritionDensity(calculateNutritionDensity(dietSuggestion));
        
        // 生成营养评估
        analysis.setNutritionScore(calculateNutritionScore(macros));
        analysis.setNutritionGrade(getNutritionGrade(analysis.getNutritionScore()));
        
        // 生成改进建议
        analysis.setImprovementSuggestions(generateNutritionImprovements(macros));
        
        return analysis;
    }
    
    /**
     * 分析蛋白质充足性
     */
    private static String analyzeProteinAdequacy(NutritionCalculator.MacronutrientDistribution macros) {
        double proteinPercentage = macros.getProteinPercentage();
        
        if (proteinPercentage >= NutritionCalculator.OPTIMAL_PROTEIN_PERCENTAGE) {
            return "蛋白质摄入充足，有助于维持肌肉量和新陈代谢";
        } else if (proteinPercentage >= NutritionCalculator.MIN_PROTEIN_PERCENTAGE) {
            return "蛋白质摄入基本满足需求，建议适当增加优质蛋白质";
        } else {
            return "蛋白质摄入不足，建议增加鸡蛋、瘦肉、豆类等蛋白质来源";
        }
    }
    
    /**
     * 分析碳水化合物充足性
     */
    private static String analyzeCarbAdequacy(NutritionCalculator.MacronutrientDistribution macros) {
        double carbPercentage = macros.getCarbPercentage();
        
        if (carbPercentage >= NutritionCalculator.OPTIMAL_CARB_PERCENTAGE) {
            return "碳水化合物摄入适中，为身体提供充足能量";
        } else if (carbPercentage >= NutritionCalculator.MIN_CARB_PERCENTAGE) {
            return "碳水化合物摄入基本合理，注意选择复合碳水化合物";
        } else {
            return "碳水化合物摄入偏低，建议增加全谷物、薯类等健康碳水";
        }
    }
    
    /**
     * 分析脂肪充足性
     */
    private static String analyzeFatAdequacy(NutritionCalculator.MacronutrientDistribution macros) {
        double fatPercentage = macros.getFatPercentage();
        
        if (fatPercentage <= NutritionCalculator.OPTIMAL_FAT_PERCENTAGE) {
            return "脂肪摄入适量，注意选择健康脂肪如橄榄油、坚果";
        } else if (fatPercentage <= NutritionCalculator.MAX_FAT_PERCENTAGE) {
            return "脂肪摄入略高，建议减少饱和脂肪，增加不饱和脂肪";
        } else {
            return "脂肪摄入过高，建议减少油炸食品和高脂肪食物";
        }
    }
    
    /**
     * 计算营养密度
     */
    private static double calculateNutritionDensity(DietSuggestionEngine.DietSuggestion dietSuggestion) {
        // 简化的营养密度计算：基于推荐食物的营养价值
        double totalScore = 0;
        int foodCount = 0;
        
        for (DietSuggestionEngine.MealSuggestion meal : dietSuggestion.getMealSuggestions()) {
            for (FoodDatabase.FoodRecommendation food : meal.getFoodRecommendations()) {
                FoodDatabase.FoodItem foodItem = FoodDatabase.getFoodByName(food.getFoodName());
                if (foodItem != null) {
                    // 营养密度 = (蛋白质 + 纤维估值) / 热量 * 100
                    double density = (foodItem.getProteinPer100g() + estimateFiber(foodItem)) / 
                                   foodItem.getCaloriesPer100g() * 100;
                    totalScore += density;
                    foodCount++;
                }
            }
        }
        
        return foodCount > 0 ? totalScore / foodCount : 0;
    }
    
    /**
     * 估算食物纤维含量
     */
    private static double estimateFiber(FoodDatabase.FoodItem food) {
        // 简化的纤维估算
        if (FoodDatabase.CATEGORY_VEGETABLE.equals(food.getCategory()) || 
            FoodDatabase.CATEGORY_FRUIT.equals(food.getCategory())) {
            return food.getCarbsPer100g() * 0.3; // 蔬果纤维约为碳水的30%
        } else if (FoodDatabase.CATEGORY_CARB.equals(food.getCategory())) {
            return food.getCarbsPer100g() * 0.1; // 谷物纤维约为碳水的10%
        }
        return 0;
    }
    
    /**
     * 计算营养评分
     */
    private static int calculateNutritionScore(NutritionCalculator.MacronutrientDistribution macros) {
        int score = 100;
        
        // 蛋白质评分
        double proteinPercentage = macros.getProteinPercentage();
        if (proteinPercentage < NutritionCalculator.MIN_PROTEIN_PERCENTAGE) {
            score -= 20;
        } else if (proteinPercentage > NutritionCalculator.MAX_PROTEIN_PERCENTAGE) {
            score -= 10;
        }
        
        // 碳水化合物评分
        double carbPercentage = macros.getCarbPercentage();
        if (carbPercentage < NutritionCalculator.MIN_CARB_PERCENTAGE) {
            score -= 15;
        } else if (carbPercentage > NutritionCalculator.MAX_CARB_PERCENTAGE) {
            score -= 10;
        }
        
        // 脂肪评分
        double fatPercentage = macros.getFatPercentage();
        if (fatPercentage < NutritionCalculator.MIN_FAT_PERCENTAGE) {
            score -= 15;
        } else if (fatPercentage > NutritionCalculator.MAX_FAT_PERCENTAGE) {
            score -= 20;
        }
        
        return Math.max(0, Math.min(100, score));
    }
    
    /**
     * 获取营养等级
     */
    private static String getNutritionGrade(int score) {
        if (score >= 90) return "优秀";
        else if (score >= 80) return "良好";
        else if (score >= 70) return "一般";
        else if (score >= 60) return "需改进";
        else return "不合格";
    }
    
    /**
     * 生成营养改进建议
     */
    private static List<String> generateNutritionImprovements(NutritionCalculator.MacronutrientDistribution macros) {
        List<String> improvements = new ArrayList<>();
        
        // 检查蛋白质
        if (macros.getProteinPercentage() < NutritionCalculator.MIN_PROTEIN_PERCENTAGE) {
            improvements.add("增加优质蛋白质摄入，如鸡胸肉、鱼类、豆类");
        }
        
        // 检查碳水化合物
        if (macros.getCarbPercentage() < NutritionCalculator.MIN_CARB_PERCENTAGE) {
            improvements.add("适当增加复合碳水化合物，如全谷物、薯类");
        } else if (macros.getCarbPercentage() > NutritionCalculator.MAX_CARB_PERCENTAGE) {
            improvements.add("减少精制碳水化合物，选择高纤维的复合碳水");
        }
        
        // 检查脂肪
        if (macros.getFatPercentage() > NutritionCalculator.MAX_FAT_PERCENTAGE) {
            improvements.add("减少饱和脂肪摄入，选择健康脂肪如橄榄油、坚果");
        }
        
        // 通用建议
        improvements.add("增加蔬菜水果摄入，补充维生素和矿物质");
        improvements.add("保持充足水分摄入，每天1.5-2升");
        improvements.add("选择多样化的食物，确保营养全面");
        
        return improvements;
    }
    
    /**
     * 生成实施建议
     * @param user 用户信息
     * @param dietSuggestion 饮食建议
     * @return 实施建议列表
     */
    private static List<String> generateImplementationTips(User user, DietSuggestionEngine.DietSuggestion dietSuggestion) {
        List<String> tips = new ArrayList<>();
        
        // 基本实施建议
        tips.add("制定每周菜单计划，提前准备食材");
        tips.add("使用食物秤或量杯，准确控制分量");
        tips.add("记录饮食日记，监控摄入情况");
        tips.add("准备健康零食，避免饥饿时的不良选择");
        
        // 根据目标调整建议
        String goal = dietSuggestion.getGoal();
        if (DietSuggestionEngine.GOAL_WEIGHT_LOSS.equals(goal)) {
            tips.add("餐前喝水，使用小盘子控制分量");
            tips.add("慢慢进食，增强饱腹感");
            tips.add("避免在看电视时进食");
        } else if (DietSuggestionEngine.GOAL_WEIGHT_GAIN.equals(goal)) {
            tips.add("增加餐次频率，每2-3小时进食一次");
            tips.add("在食物中添加健康脂肪，如坚果、牛油果");
            tips.add("运动后及时补充营养");
        }
        
        // 根据年龄调整建议
        if (user.getAge() >= 50) {
            tips.add("注意食物质地，选择易消化的食物");
            tips.add("补充钙质和维生素D，预防骨质疏松");
        }
        
        // 实用技巧
        tips.add("批量烹饪，周末准备一周的食材");
        tips.add("学习健康烹饪方法，如蒸、煮、烤");
        tips.add("外出就餐时，选择健康选项，控制分量");
        tips.add("保持规律的进餐时间，避免暴饮暴食");
        
        return tips;
    }
    
    /**
     * 完整饮食计划类
     */
    public static class CompleteDietPlan {
        private boolean valid = false;
        private String errorMessage = "";
        private User user;
        private CalculatorUtils.HealthCalculationResult healthResult;
        private DietSuggestionEngine.DietSuggestion dietSuggestion;
        private List<String> shoppingList;
        private NutritionAnalysis nutritionAnalysis;
        private List<String> implementationTips;
        private int timeFrameDays;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public User getUser() { return user; }
        public void setUser(User user) { this.user = user; }
        
        public CalculatorUtils.HealthCalculationResult getHealthResult() { return healthResult; }
        public void setHealthResult(CalculatorUtils.HealthCalculationResult healthResult) { this.healthResult = healthResult; }
        
        public DietSuggestionEngine.DietSuggestion getDietSuggestion() { return dietSuggestion; }
        public void setDietSuggestion(DietSuggestionEngine.DietSuggestion dietSuggestion) { this.dietSuggestion = dietSuggestion; }
        
        public List<String> getShoppingList() { return shoppingList; }
        public void setShoppingList(List<String> shoppingList) { this.shoppingList = shoppingList; }
        
        public NutritionAnalysis getNutritionAnalysis() { return nutritionAnalysis; }
        public void setNutritionAnalysis(NutritionAnalysis nutritionAnalysis) { this.nutritionAnalysis = nutritionAnalysis; }
        
        public List<String> getImplementationTips() { return implementationTips; }
        public void setImplementationTips(List<String> implementationTips) { this.implementationTips = implementationTips; }
        
        public int getTimeFrameDays() { return timeFrameDays; }
        public void setTimeFrameDays(int timeFrameDays) { this.timeFrameDays = timeFrameDays; }
        
        @Override
        public String toString() {
            return String.format("CompleteDietPlan{valid=%s, goal=%s, calories=%.0f, meals=%d}", 
                               valid, dietSuggestion != null ? dietSuggestion.getGoal() : "unknown", 
                               dietSuggestion != null ? dietSuggestion.getTotalCalories() : 0,
                               dietSuggestion != null && dietSuggestion.getMealSuggestions() != null ? 
                               dietSuggestion.getMealSuggestions().size() : 0);
        }
    }
    
    /**
     * 营养分析类
     */
    public static class NutritionAnalysis {
        private String proteinAdequacy;
        private String carbAdequacy;
        private String fatAdequacy;
        private double nutritionDensity;
        private int nutritionScore;
        private String nutritionGrade;
        private List<String> improvementSuggestions;
        
        // Getters and Setters
        public String getProteinAdequacy() { return proteinAdequacy; }
        public void setProteinAdequacy(String proteinAdequacy) { this.proteinAdequacy = proteinAdequacy; }
        
        public String getCarbAdequacy() { return carbAdequacy; }
        public void setCarbAdequacy(String carbAdequacy) { this.carbAdequacy = carbAdequacy; }
        
        public String getFatAdequacy() { return fatAdequacy; }
        public void setFatAdequacy(String fatAdequacy) { this.fatAdequacy = fatAdequacy; }
        
        public double getNutritionDensity() { return nutritionDensity; }
        public void setNutritionDensity(double nutritionDensity) { this.nutritionDensity = nutritionDensity; }
        
        public int getNutritionScore() { return nutritionScore; }
        public void setNutritionScore(int nutritionScore) { this.nutritionScore = nutritionScore; }
        
        public String getNutritionGrade() { return nutritionGrade; }
        public void setNutritionGrade(String nutritionGrade) { this.nutritionGrade = nutritionGrade; }
        
        public List<String> getImprovementSuggestions() { return improvementSuggestions; }
        public void setImprovementSuggestions(List<String> improvementSuggestions) { this.improvementSuggestions = improvementSuggestions; }
    }
}
