package com.healthydiet.utils;

/**
 * 数据验证工具类
 * 提供各种数据验证方法，确保输入数据的有效性
 */
public class ValidationUtils {
    
    // 有效范围常量
    public static final double MIN_WEIGHT = 20.0; // 最小体重 (kg)
    public static final double MAX_WEIGHT = 500.0; // 最大体重 (kg)
    public static final double MIN_HEIGHT = 50.0; // 最小身高 (cm)
    public static final double MAX_HEIGHT = 300.0; // 最大身高 (cm)
    public static final int MIN_AGE = 1; // 最小年龄
    public static final int MAX_AGE = 150; // 最大年龄
    public static final double MIN_BMR = 500.0; // 最小BMR值
    public static final double MAX_BMR = 5000.0; // 最大BMR值
    
    // 性别常量
    public static final String GENDER_MALE = "male";
    public static final String GENDER_FEMALE = "female";
    
    /**
     * 验证体重是否有效
     * @param weight 体重 (kg)
     * @return 有效返回true
     */
    public static boolean isValidWeight(double weight) {
        return weight >= MIN_WEIGHT && weight <= MAX_WEIGHT && !Double.isNaN(weight) && Double.isFinite(weight);
    }
    
    /**
     * 验证身高是否有效
     * @param height 身高 (cm)
     * @return 有效返回true
     */
    public static boolean isValidHeight(double height) {
        return height >= MIN_HEIGHT && height <= MAX_HEIGHT && !Double.isNaN(height) && Double.isFinite(height);
    }
    
    /**
     * 验证年龄是否有效
     * @param age 年龄
     * @return 有效返回true
     */
    public static boolean isValidAge(int age) {
        return age >= MIN_AGE && age <= MAX_AGE;
    }
    
    /**
     * 验证性别是否有效
     * @param gender 性别
     * @return 有效返回true
     */
    public static boolean isValidGender(String gender) {
        return gender != null && (GENDER_MALE.equalsIgnoreCase(gender) || GENDER_FEMALE.equalsIgnoreCase(gender));
    }
    
    /**
     * 验证BMR值是否有效
     * @param bmr BMR值
     * @return 有效返回true
     */
    public static boolean isValidBMR(double bmr) {
        return bmr >= MIN_BMR && bmr <= MAX_BMR && !Double.isNaN(bmr) && Double.isFinite(bmr);
    }
    
    /**
     * 验证活动系数是否有效
     * @param activityFactor 活动系数
     * @return 有效返回true
     */
    public static boolean isValidActivityFactor(double activityFactor) {
        return activityFactor >= 1.0 && activityFactor <= 2.5 && !Double.isNaN(activityFactor) && Double.isFinite(activityFactor);
    }
    
    /**
     * 验证热量值是否有效
     * @param calories 热量值
     * @return 有效返回true
     */
    public static boolean isValidCalories(double calories) {
        return calories >= 0 && calories <= 10000 && !Double.isNaN(calories) && Double.isFinite(calories);
    }
    
    /**
     * 验证时间范围是否有效（天数）
     * @param days 天数
     * @return 有效返回true
     */
    public static boolean isValidTimeFrame(int days) {
        return days > 0 && days <= 3650; // 最多10年
    }
    
    /**
     * 验证字符串是否非空
     * @param str 字符串
     * @return 非空返回true
     */
    public static boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }
    
    /**
     * 验证数值是否为正数
     * @param value 数值
     * @return 正数返回true
     */
    public static boolean isPositive(double value) {
        return value > 0 && !Double.isNaN(value) && Double.isFinite(value);
    }
    
    /**
     * 验证数值是否为非负数
     * @param value 数值
     * @return 非负数返回true
     */
    public static boolean isNonNegative(double value) {
        return value >= 0 && !Double.isNaN(value) && Double.isFinite(value);
    }
    
    /**
     * 验证数值是否在指定范围内
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return 在范围内返回true
     */
    public static boolean isInRange(double value, double min, double max) {
        return value >= min && value <= max && !Double.isNaN(value) && Double.isFinite(value);
    }
    
    /**
     * 验证整数是否在指定范围内
     * @param value 整数
     * @param min 最小值
     * @param max 最大值
     * @return 在范围内返回true
     */
    public static boolean isInRange(int value, int min, int max) {
        return value >= min && value <= max;
    }
    
    /**
     * 安全地将字符串转换为double
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 转换结果
     */
    public static double safeParseDouble(String str, double defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            double value = Double.parseDouble(str.trim());
            return Double.isFinite(value) ? value : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全地将字符串转换为int
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 转换结果
     */
    public static int safeParseInt(String str, int defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 格式化数值为指定小数位数
     * @param value 数值
     * @param decimalPlaces 小数位数
     * @return 格式化后的数值
     */
    public static double formatDecimal(double value, int decimalPlaces) {
        if (!Double.isFinite(value)) {
            return 0.0;
        }
        double factor = Math.pow(10, decimalPlaces);
        return Math.round(value * factor) / factor;
    }
    
    /**
     * 验证用户基本信息的完整性
     * @param name 姓名
     * @param gender 性别
     * @param age 年龄
     * @param height 身高
     * @param weight 体重
     * @return 验证结果
     */
    public static ValidationResult validateUserInfo(String name, String gender, int age, double height, double weight) {
        ValidationResult result = new ValidationResult();
        
        if (!isNotEmpty(name)) {
            result.addError("姓名不能为空");
        }
        
        if (!isValidGender(gender)) {
            result.addError("性别必须是 'male' 或 'female'");
        }
        
        if (!isValidAge(age)) {
            result.addError("年龄必须在 " + MIN_AGE + " 到 " + MAX_AGE + " 之间");
        }
        
        if (!isValidHeight(height)) {
            result.addError("身高必须在 " + MIN_HEIGHT + " 到 " + MAX_HEIGHT + " cm 之间");
        }
        
        if (!isValidWeight(weight)) {
            result.addError("体重必须在 " + MIN_WEIGHT + " 到 " + MAX_WEIGHT + " kg 之间");
        }
        
        return result;
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private StringBuilder errors = new StringBuilder();
        private boolean valid = true;
        
        public void addError(String error) {
            if (errors.length() > 0) {
                errors.append("; ");
            }
            errors.append(error);
            valid = false;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrors() {
            return errors.toString();
        }
        
        public boolean hasErrors() {
            return !valid;
        }
    }
}
