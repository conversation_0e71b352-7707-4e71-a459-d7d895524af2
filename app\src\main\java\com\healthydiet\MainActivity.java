package com.healthydiet;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.healthydiet.calculator.CalculatorUtils;
import com.healthydiet.database.DatabaseManager;
import com.healthydiet.model.User;
import com.healthydiet.suggestion.DietSuggestionEngine;

/**
 * 主界面Activity - 应用的入口点
 * 显示用户基本信息和导航功能
 */
public class MainActivity extends AppCompatActivity implements BottomNavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "MainActivity";

    // UI组件
    private TextView tvCurrentWeight;
    private TextView tvBMR;
    private TextView tvCalorieDeficit;
    private TextView tvTodaySuggestion;
    private BottomNavigationView bottomNavigation;
    private SwipeRefreshLayout swipeRefreshLayout;

    // 数据管理
    private DatabaseManager databaseManager;
    private User currentUser;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化数据管理器
        databaseManager = DatabaseManager.getInstance(this);

        // 初始化界面组件
        initViews();

        // 设置底部导航栏
        setupBottomNavigation();

        // 加载用户数据
        loadUserData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次回到主界面时刷新数据
        loadUserData();
    }

    /**
     * 初始化界面组件
     */
    private void initViews() {
        tvCurrentWeight = findViewById(R.id.tvCurrentWeight);
        tvBMR = findViewById(R.id.tvBMR);
        tvCalorieDeficit = findViewById(R.id.tvCalorieDeficit);
        tvTodaySuggestion = findViewById(R.id.tvTodaySuggestion);
        bottomNavigation = findViewById(R.id.bottomNavigation);

        // 添加下拉刷新功能
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setOnRefreshListener(this::refreshData);
            swipeRefreshLayout.setColorSchemeResources(
                R.color.healthy_green,
                R.color.info_blue,
                R.color.warning_orange
            );
        }
    }

    /**
     * 设置底部导航栏
     */
    private void setupBottomNavigation() {
        bottomNavigation.setOnNavigationItemSelectedListener(this);
        bottomNavigation.setSelectedItemId(R.id.nav_home);
    }

    /**
     * 加载用户数据
     */
    private void loadUserData() {
        try {
            // 检查数据库连接
            if (!databaseManager.checkDatabaseConnection()) {
                showError("数据库连接失败");
                return;
            }

            // 获取当前用户
            currentUser = databaseManager.getCurrentUser();

            if (currentUser == null) {
                // 没有用户数据，跳转到设置页面
                showNoUserData();
                return;
            }

            // 显示用户基本信息
            displayUserInfo();

            // 计算并显示健康数据
            calculateAndDisplayHealthData();

        } catch (Exception e) {
            Log.e(TAG, "加载用户数据失败", e);
            showError("加载数据失败：" + e.getMessage());
        }
    }

    /**
     * 显示用户基本信息
     */
    private void displayUserInfo() {
        if (currentUser != null) {
            tvCurrentWeight.setText(String.format("%.1f kg", currentUser.getWeight()));
        } else {
            tvCurrentWeight.setText("-- kg");
        }
    }

    /**
     * 计算并显示健康数据
     */
    private void calculateAndDisplayHealthData() {
        if (currentUser == null) return;

        try {
            // 使用中等活动水平作为默认值
            String defaultActivityLevel = "moderate";
            int defaultTimeFrame = 90; // 90天

            // 计算完整健康数据
            CalculatorUtils.HealthCalculationResult healthResult =
                CalculatorUtils.calculateCompleteHealthData(currentUser, defaultActivityLevel, defaultTimeFrame);

            if (healthResult.isValid()) {
                // 显示BMR
                tvBMR.setText(String.format("%.0f kcal", healthResult.getBmr()));

                // 显示热量缺口
                double calorieDeficit = healthResult.getCalorieDeficit();
                if (calorieDeficit > 0) {
                    tvCalorieDeficit.setText(String.format("-%.0f kcal", calorieDeficit));
                } else if (calorieDeficit < 0) {
                    tvCalorieDeficit.setText(String.format("+%.0f kcal", Math.abs(calorieDeficit)));
                } else {
                    tvCalorieDeficit.setText("0 kcal");
                }

                // 生成并显示今日建议
                generateTodaySuggestion(healthResult);

            } else {
                Log.e(TAG, "健康数据计算失败: " + healthResult.getErrorMessage());
                tvBMR.setText("计算失败");
                tvCalorieDeficit.setText("计算失败");
                tvTodaySuggestion.setText("无法生成建议，请检查用户数据");
            }

        } catch (Exception e) {
            Log.e(TAG, "计算健康数据失败", e);
            showError("计算失败：" + e.getMessage());
        }
    }

    /**
     * 生成今日建议
     */
    private void generateTodaySuggestion(CalculatorUtils.HealthCalculationResult healthResult) {
        try {
            // 生成饮食建议
            DietSuggestionEngine.DietSuggestion dietSuggestion =
                DietSuggestionEngine.generateSuggestion(currentUser, healthResult.getRecommendedIntake(), "moderate");

            if (dietSuggestion.isValid()) {
                // 显示简化的建议文本
                String suggestion = generateSimpleSuggestionText(dietSuggestion, healthResult);
                tvTodaySuggestion.setText(suggestion);
            } else {
                tvTodaySuggestion.setText("无法生成饮食建议");
            }

        } catch (Exception e) {
            Log.e(TAG, "生成今日建议失败", e);
            tvTodaySuggestion.setText("建议生成失败");
        }
    }

    /**
     * 生成简化的建议文本
     */
    private String generateSimpleSuggestionText(DietSuggestionEngine.DietSuggestion dietSuggestion,
                                               CalculatorUtils.HealthCalculationResult healthResult) {
        StringBuilder suggestion = new StringBuilder();

        // 基本热量建议
        suggestion.append(String.format("建议每日摄入%.0f卡路里", dietSuggestion.getTotalCalories()));

        // 目标相关建议
        String goal = dietSuggestion.getGoal();
        if ("weight_loss".equals(goal)) {
            suggestion.append("，重点控制分量，增加蛋白质摄入");
        } else if ("weight_gain".equals(goal)) {
            suggestion.append("，适当增加营养密度高的食物");
        } else {
            suggestion.append("，保持营养均衡");
        }

        // 添加一个具体的行动建议
        if (dietSuggestion.getMealSuggestions() != null && !dietSuggestion.getMealSuggestions().isEmpty()) {
            DietSuggestionEngine.MealSuggestion breakfast = dietSuggestion.getMealSuggestions().get(0);
            if (breakfast.getFoodRecommendations() != null && !breakfast.getFoodRecommendations().isEmpty()) {
                String firstFood = breakfast.getFoodRecommendations().get(0).getFoodName();
                suggestion.append("。今日早餐推荐：").append(firstFood).append("等");
            }
        }

        return suggestion.toString();
    }

    /**
     * 显示没有用户数据的提示
     */
    private void showNoUserData() {
        tvCurrentWeight.setText("-- kg");
        tvBMR.setText("-- kcal");
        tvCalorieDeficit.setText("-- kcal");
        tvTodaySuggestion.setText("请先在设置中完善个人信息");

        Toast.makeText(this, "请先设置个人信息", Toast.LENGTH_LONG).show();
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        Log.e(TAG, message);
    }

    /**
     * 刷新数据
     */
    private void refreshData() {
        loadUserData();
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(false);
        }
        Toast.makeText(this, "数据已刷新", Toast.LENGTH_SHORT).show();
    }

    /**
     * 底部导航栏点击事件处理
     */
    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == R.id.nav_home) {
            // 已经在主页，刷新数据
            refreshData();
            return true;
        } else if (itemId == R.id.nav_calculate) {
            // 跳转到计算页面
            startActivity(new Intent(this, CalculateActivity.class));
            return true;
        } else if (itemId == R.id.nav_suggestion) {
            // 跳转到建议页面
            startActivity(new Intent(this, SuggestionActivity.class));
            return true;
        } else if (itemId == R.id.nav_history) {
            // 跳转到历史页面
            startActivity(new Intent(this, HistoryActivity.class));
            return true;
        } else if (itemId == R.id.nav_settings) {
            // 跳转到设置页面
            startActivity(new Intent(this, SettingsActivity.class));
            return true;
        }

        return false;
    }
}
