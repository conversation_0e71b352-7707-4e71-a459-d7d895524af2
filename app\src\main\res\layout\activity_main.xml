<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- 主要内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottomNavigation">

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 欢迎信息 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/welcome_message"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 用户信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/current_weight"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                    <TextView
                        android:id="@+id/tvCurrentWeight"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-- kg"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:textColor="@color/healthy_green" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- BMR信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/bmr_value"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                    <TextView
                        android:id="@+id/tvBMR"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-- kcal"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:textColor="@color/info_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 热量缺口卡片 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/calorie_deficit"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                    <TextView
                        android:id="@+id/tvCalorieDeficit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="-- kcal"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Headline"
                        android:textColor="@color/warning_orange" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 今日建议卡片 -->
            <com.google.android.material.card.MaterialCardView
                style="@style/Widget.HealthyDiet.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/today_suggestion"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Body" />

                    <TextView
                        android:id="@+id/tvTodaySuggestion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/no_data"
                        android:textAppearance="@style/TextAppearance.HealthyDiet.Caption"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        </ScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 底部导航栏 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:menu="@menu/bottom_navigation"
        app:labelVisibilityMode="labeled" />

</androidx.constraintlayout.widget.ConstraintLayout>
