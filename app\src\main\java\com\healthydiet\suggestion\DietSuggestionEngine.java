package com.healthydiet.suggestion;

import android.util.Log;

import com.healthydiet.calculator.CalorieDeficitCalculator;
import com.healthydiet.calculator.NutritionCalculator;
import com.healthydiet.data.FoodDatabase;
import com.healthydiet.model.User;
import com.healthydiet.utils.ValidationUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 饮食建议引擎
 * 基于用户信息和热量需求生成个性化饮食建议
 */
public class DietSuggestionEngine {
    private static final String TAG = "DietSuggestionEngine";
    
    // 餐次热量分配比例
    private static final double BREAKFAST_RATIO = 0.25; // 早餐25%
    private static final double LUNCH_RATIO = 0.35; // 午餐35%
    private static final double DINNER_RATIO = 0.30; // 晚餐30%
    private static final double SNACK_RATIO = 0.10; // 加餐10%
    
    // 目标类型常量
    public static final String GOAL_WEIGHT_LOSS = "weight_loss";
    public static final String GOAL_WEIGHT_GAIN = "weight_gain";
    public static final String GOAL_MAINTAIN = "maintain";
    public static final String GOAL_MUSCLE_GAIN = "muscle_gain";
    
    /**
     * 生成完整的饮食建议
     * @param user 用户信息
     * @param recommendedCalories 建议摄入热量
     * @param activityLevel 活动水平
     * @return 完整饮食建议
     */
    public static DietSuggestion generateSuggestion(User user, double recommendedCalories, String activityLevel) {
        DietSuggestion suggestion = new DietSuggestion();
        
        if (user == null || !user.isValid()) {
            suggestion.setValid(false);
            suggestion.setErrorMessage("用户信息无效");
            return suggestion;
        }
        
        if (!ValidationUtils.isValidCalories(recommendedCalories)) {
            suggestion.setValid(false);
            suggestion.setErrorMessage("推荐热量无效: " + recommendedCalories);
            return suggestion;
        }
        
        try {
            // 确定目标类型
            String goal = determineGoal(user);
            
            // 计算最佳营养素分配
            NutritionCalculator.MacronutrientDistribution macros = 
                NutritionCalculator.calculateOptimalMacronutrients(
                    recommendedCalories, goal, activityLevel, user.getAge(), user.getWeight());
            
            if (!macros.isValid()) {
                suggestion.setValid(false);
                suggestion.setErrorMessage("营养素分配计算失败: " + macros.getErrorMessage());
                return suggestion;
            }
            
            // 生成餐次建议
            List<MealSuggestion> mealSuggestions = generateMealSuggestions(macros, goal);
            
            // 生成总体建议文本
            String adviceText = generateAdviceText(user, macros, goal, activityLevel);
            
            // 生成健康提示
            List<String> healthTips = generateHealthTips(user, goal, activityLevel);
            
            // 设置建议结果
            suggestion.setValid(true);
            suggestion.setUser(user);
            suggestion.setGoal(goal);
            suggestion.setTotalCalories(recommendedCalories);
            suggestion.setMacronutrients(macros);
            suggestion.setMealSuggestions(mealSuggestions);
            suggestion.setAdviceText(adviceText);
            suggestion.setHealthTips(healthTips);
            suggestion.setActivityLevel(activityLevel);
            
            Log.d(TAG, "饮食建议生成成功: " + suggestion.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "生成饮食建议过程中发生异常", e);
            suggestion.setValid(false);
            suggestion.setErrorMessage("生成建议过程中发生异常: " + e.getMessage());
        }
        
        return suggestion;
    }
    
    /**
     * 确定用户目标
     * @param user 用户信息
     * @return 目标类型
     */
    private static String determineGoal(User user) {
        double weightDiff = user.getWeight() - user.getTargetWeight();
        
        if (Math.abs(weightDiff) <= 1.0) {
            return GOAL_MAINTAIN;
        } else if (weightDiff > 1.0) {
            return GOAL_WEIGHT_LOSS;
        } else {
            return GOAL_WEIGHT_GAIN;
        }
    }
    
    /**
     * 生成餐次建议
     * @param macros 营养素分配
     * @param goal 目标类型
     * @return 餐次建议列表
     */
    private static List<MealSuggestion> generateMealSuggestions(NutritionCalculator.MacronutrientDistribution macros, String goal) {
        List<MealSuggestion> mealSuggestions = new ArrayList<>();
        
        // 早餐建议
        MealSuggestion breakfast = generateMealSuggestion(
            FoodDatabase.MEAL_BREAKFAST, macros, BREAKFAST_RATIO, goal);
        mealSuggestions.add(breakfast);
        
        // 午餐建议
        MealSuggestion lunch = generateMealSuggestion(
            FoodDatabase.MEAL_LUNCH, macros, LUNCH_RATIO, goal);
        mealSuggestions.add(lunch);
        
        // 晚餐建议
        MealSuggestion dinner = generateMealSuggestion(
            FoodDatabase.MEAL_DINNER, macros, DINNER_RATIO, goal);
        mealSuggestions.add(dinner);
        
        // 加餐建议（如果需要）
        if (macros.getTotalCalories() > 1800) { // 高热量需求时添加加餐
            MealSuggestion snack = generateMealSuggestion(
                FoodDatabase.MEAL_SNACK, macros, SNACK_RATIO, goal);
            mealSuggestions.add(snack);
        }
        
        return mealSuggestions;
    }
    
    /**
     * 生成单餐建议
     * @param mealType 餐次类型
     * @param macros 总营养素分配
     * @param ratio 该餐次占比
     * @param goal 目标类型
     * @return 单餐建议
     */
    private static MealSuggestion generateMealSuggestion(String mealType, 
                                                        NutritionCalculator.MacronutrientDistribution macros, 
                                                        double ratio, 
                                                        String goal) {
        MealSuggestion mealSuggestion = new MealSuggestion();
        mealSuggestion.setMealType(mealType);
        mealSuggestion.setMealName(getMealName(mealType));
        
        // 计算该餐次的营养需求
        double mealCalories = macros.getTotalCalories() * ratio;
        double mealProtein = macros.getProteinGrams() * ratio;
        double mealCarbs = macros.getCarbGrams() * ratio;
        double mealFat = macros.getFatGrams() * ratio;
        
        mealSuggestion.setTargetCalories(ValidationUtils.formatDecimal(mealCalories, 0));
        mealSuggestion.setTargetProtein(ValidationUtils.formatDecimal(mealProtein, 1));
        mealSuggestion.setTargetCarbs(ValidationUtils.formatDecimal(mealCarbs, 1));
        mealSuggestion.setTargetFat(ValidationUtils.formatDecimal(mealFat, 1));
        
        // 获取食物推荐
        List<FoodDatabase.FoodRecommendation> foodRecommendations = 
            FoodDatabase.recommendFoodsForNutrition(mealProtein, mealCarbs, mealFat, mealType);
        mealSuggestion.setFoodRecommendations(foodRecommendations);
        
        // 生成餐次描述
        String description = generateMealDescription(mealType, foodRecommendations, goal);
        mealSuggestion.setDescription(description);
        
        return mealSuggestion;
    }
    
    /**
     * 获取餐次名称
     * @param mealType 餐次类型
     * @return 餐次名称
     */
    private static String getMealName(String mealType) {
        switch (mealType) {
            case FoodDatabase.MEAL_BREAKFAST:
                return "早餐";
            case FoodDatabase.MEAL_LUNCH:
                return "午餐";
            case FoodDatabase.MEAL_DINNER:
                return "晚餐";
            case FoodDatabase.MEAL_SNACK:
                return "加餐";
            default:
                return "未知餐次";
        }
    }
    
    /**
     * 生成餐次描述
     * @param mealType 餐次类型
     * @param recommendations 食物推荐
     * @param goal 目标类型
     * @return 餐次描述
     */
    private static String generateMealDescription(String mealType, 
                                                 List<FoodDatabase.FoodRecommendation> recommendations, 
                                                 String goal) {
        StringBuilder description = new StringBuilder();
        
        // 添加餐次特点描述
        switch (mealType) {
            case FoodDatabase.MEAL_BREAKFAST:
                description.append("营养均衡的早餐，为一天提供充足能量。");
                break;
            case FoodDatabase.MEAL_LUNCH:
                description.append("丰富的午餐，补充上午消耗的能量。");
                break;
            case FoodDatabase.MEAL_DINNER:
                description.append("清淡的晚餐，避免过多热量摄入。");
                break;
            case FoodDatabase.MEAL_SNACK:
                description.append("健康加餐，补充营养和能量。");
                break;
        }
        
        // 添加目标相关建议
        if (GOAL_WEIGHT_LOSS.equals(goal)) {
            description.append("注意控制分量，选择低热量高营养的食物。");
        } else if (GOAL_WEIGHT_GAIN.equals(goal)) {
            description.append("适当增加分量，选择营养密度高的食物。");
        } else if (GOAL_MUSCLE_GAIN.equals(goal)) {
            description.append("重点补充蛋白质，支持肌肉生长。");
        }
        
        // 添加食物组合建议
        if (!recommendations.isEmpty()) {
            description.append("推荐搭配：");
            for (int i = 0; i < Math.min(3, recommendations.size()); i++) {
                FoodDatabase.FoodRecommendation rec = recommendations.get(i);
                if (i > 0) description.append("、");
                description.append(rec.getFoodName());
            }
            description.append("等。");
        }
        
        return description.toString();
    }
    
    /**
     * 生成总体建议文本
     * @param user 用户信息
     * @param macros 营养素分配
     * @param goal 目标类型
     * @param activityLevel 活动水平
     * @return 建议文本
     */
    private static String generateAdviceText(User user, 
                                           NutritionCalculator.MacronutrientDistribution macros, 
                                           String goal, 
                                           String activityLevel) {
        StringBuilder advice = new StringBuilder();
        
        // 基本建议
        advice.append(String.format("根据您的个人情况，建议每日摄入%.0f卡路里，", macros.getTotalCalories()));
        advice.append(String.format("其中蛋白质%.1fg（%.0f%%），", 
                     macros.getProteinGrams(), macros.getProteinPercentage() * 100));
        advice.append(String.format("碳水化合物%.1fg（%.0f%%），", 
                     macros.getCarbGrams(), macros.getCarbPercentage() * 100));
        advice.append(String.format("脂肪%.1fg（%.0f%%）。", 
                     macros.getFatGrams(), macros.getFatPercentage() * 100));
        
        // 目标相关建议
        switch (goal) {
            case GOAL_WEIGHT_LOSS:
                advice.append("为了健康减重，建议采用适度的热量控制，");
                advice.append("重点增加蛋白质摄入以保持肌肉量，");
                advice.append("选择高纤维、低热量密度的食物，");
                advice.append("避免高糖、高脂肪的加工食品。");
                break;
            case GOAL_WEIGHT_GAIN:
                advice.append("为了健康增重，建议增加营养密度高的食物，");
                advice.append("适当增加健康脂肪的摄入，");
                advice.append("选择复合碳水化合物，");
                advice.append("配合适量的力量训练。");
                break;
            case GOAL_MUSCLE_GAIN:
                advice.append("为了增肌，建议重点增加优质蛋白质摄入，");
                advice.append("在训练前后合理安排碳水化合物，");
                advice.append("确保充足的热量支持肌肉生长，");
                advice.append("配合规律的力量训练。");
                break;
            case GOAL_MAINTAIN:
                advice.append("为了维持当前体重，建议保持营养均衡，");
                advice.append("适量摄入各类营养素，");
                advice.append("保持规律的饮食习惯，");
                advice.append("配合适度的运动。");
                break;
        }
        
        // 活动水平相关建议
        if (CalorieDeficitCalculator.ACTIVITY_SEDENTARY.equals(activityLevel)) {
            advice.append("由于您的活动量较少，建议适当增加日常活动，");
            advice.append("选择低热量密度的食物。");
        } else if (CalorieDeficitCalculator.ACTIVITY_HEAVY.equals(activityLevel) || 
                   CalorieDeficitCalculator.ACTIVITY_EXTREME.equals(activityLevel)) {
            advice.append("由于您的运动量较大，注意及时补充能量和水分，");
            advice.append("运动后适当补充蛋白质促进恢复。");
        }
        
        return advice.toString();
    }
    
    /**
     * 生成健康提示
     * @param user 用户信息
     * @param goal 目标类型
     * @param activityLevel 活动水平
     * @return 健康提示列表
     */
    private static List<String> generateHealthTips(User user, String goal, String activityLevel) {
        List<String> tips = new ArrayList<>();
        
        // 通用健康提示
        tips.add("每天饮水1.5-2升，保持充足水分");
        tips.add("规律作息，保证7-8小时睡眠");
        tips.add("少食多餐，避免暴饮暴食");
        tips.add("细嚼慢咽，增强饱腹感");
        tips.add("多吃新鲜蔬菜水果，补充维生素和纤维");
        
        // 目标相关提示
        switch (goal) {
            case GOAL_WEIGHT_LOSS:
                tips.add("餐前喝一杯水，增加饱腹感");
                tips.add("选择蒸、煮、烤等低油烹饪方式");
                tips.add("避免高糖饮料和零食");
                tips.add("增加日常活动量，如走路、爬楼梯");
                break;
            case GOAL_WEIGHT_GAIN:
                tips.add("适当增加餐次，可以加1-2次健康加餐");
                tips.add("选择营养密度高的食物，如坚果、牛油果");
                tips.add("运动后及时补充营养");
                break;
            case GOAL_MUSCLE_GAIN:
                tips.add("运动后30分钟内补充蛋白质");
                tips.add("保证充足的碳水化合物支持训练");
                tips.add("规律进行力量训练");
                break;
        }
        
        // 年龄相关提示
        if (user.getAge() >= 50) {
            tips.add("注意钙质和维生素D的补充");
            tips.add("适量进行有氧运动，保持心血管健康");
        }
        
        // 活动水平相关提示
        if (CalorieDeficitCalculator.ACTIVITY_SEDENTARY.equals(activityLevel)) {
            tips.add("每小时起身活动5-10分钟");
            tips.add("尝试步行或骑车上下班");
        } else if (CalorieDeficitCalculator.ACTIVITY_HEAVY.equals(activityLevel) || 
                   CalorieDeficitCalculator.ACTIVITY_EXTREME.equals(activityLevel)) {
            tips.add("注意运动前后的营养补充");
            tips.add("避免过度训练，给身体充分恢复时间");
        }
        
        return tips;
    }
    
    /**
     * 生成简化版饮食建议
     * @param calorieDeficit 热量缺口
     * @param userProfile 用户信息
     * @return 简化建议文本
     */
    public static String generateSimpleSuggestion(double calorieDeficit, User userProfile) {
        if (userProfile == null) {
            return "用户信息无效，无法生成建议。";
        }
        
        StringBuilder suggestion = new StringBuilder();
        
        if (Math.abs(calorieDeficit) < 100) {
            suggestion.append("您的热量摄入与消耗基本平衡，建议维持当前饮食习惯。");
        } else if (calorieDeficit > 0) {
            // 需要减少摄入或增加消耗
            suggestion.append(String.format("建议每日减少%.0f卡路里摄入，", calorieDeficit));
            suggestion.append("可通过减少高热量食物、增加蔬菜摄入、适量运动来实现。");
        } else {
            // 需要增加摄入
            suggestion.append(String.format("建议每日增加%.0f卡路里摄入，", Math.abs(calorieDeficit)));
            suggestion.append("可通过增加健康脂肪、优质蛋白质、复合碳水化合物来实现。");
        }
        
        // 添加基本建议
        suggestion.append("注意营养均衡，多吃蔬菜水果，保持充足水分摄入。");
        
        return suggestion.toString();
    }
    
    /**
     * 饮食建议结果类
     */
    public static class DietSuggestion {
        private boolean valid = false;
        private String errorMessage = "";
        private User user;
        private String goal;
        private double totalCalories;
        private NutritionCalculator.MacronutrientDistribution macronutrients;
        private List<MealSuggestion> mealSuggestions;
        private String adviceText;
        private List<String> healthTips;
        private String activityLevel;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public User getUser() { return user; }
        public void setUser(User user) { this.user = user; }
        
        public String getGoal() { return goal; }
        public void setGoal(String goal) { this.goal = goal; }
        
        public double getTotalCalories() { return totalCalories; }
        public void setTotalCalories(double totalCalories) { this.totalCalories = totalCalories; }
        
        public NutritionCalculator.MacronutrientDistribution getMacronutrients() { return macronutrients; }
        public void setMacronutrients(NutritionCalculator.MacronutrientDistribution macronutrients) { this.macronutrients = macronutrients; }
        
        public List<MealSuggestion> getMealSuggestions() { return mealSuggestions; }
        public void setMealSuggestions(List<MealSuggestion> mealSuggestions) { this.mealSuggestions = mealSuggestions; }
        
        public String getAdviceText() { return adviceText; }
        public void setAdviceText(String adviceText) { this.adviceText = adviceText; }
        
        public List<String> getHealthTips() { return healthTips; }
        public void setHealthTips(List<String> healthTips) { this.healthTips = healthTips; }
        
        public String getActivityLevel() { return activityLevel; }
        public void setActivityLevel(String activityLevel) { this.activityLevel = activityLevel; }
        
        @Override
        public String toString() {
            return String.format("DietSuggestion{valid=%s, goal=%s, calories=%.0f, meals=%d}", 
                               valid, goal, totalCalories, mealSuggestions != null ? mealSuggestions.size() : 0);
        }
    }
    
    /**
     * 餐次建议类
     */
    public static class MealSuggestion {
        private String mealType;
        private String mealName;
        private double targetCalories;
        private double targetProtein;
        private double targetCarbs;
        private double targetFat;
        private List<FoodDatabase.FoodRecommendation> foodRecommendations;
        private String description;
        
        // Getters and Setters
        public String getMealType() { return mealType; }
        public void setMealType(String mealType) { this.mealType = mealType; }
        
        public String getMealName() { return mealName; }
        public void setMealName(String mealName) { this.mealName = mealName; }
        
        public double getTargetCalories() { return targetCalories; }
        public void setTargetCalories(double targetCalories) { this.targetCalories = targetCalories; }
        
        public double getTargetProtein() { return targetProtein; }
        public void setTargetProtein(double targetProtein) { this.targetProtein = targetProtein; }
        
        public double getTargetCarbs() { return targetCarbs; }
        public void setTargetCarbs(double targetCarbs) { this.targetCarbs = targetCarbs; }
        
        public double getTargetFat() { return targetFat; }
        public void setTargetFat(double targetFat) { this.targetFat = targetFat; }
        
        public List<FoodDatabase.FoodRecommendation> getFoodRecommendations() { return foodRecommendations; }
        public void setFoodRecommendations(List<FoodDatabase.FoodRecommendation> foodRecommendations) { this.foodRecommendations = foodRecommendations; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        @Override
        public String toString() {
            return String.format("%s: %.0f卡 (蛋白质%.1fg, 碳水%.1fg, 脂肪%.1fg)", 
                               mealName, targetCalories, targetProtein, targetCarbs, targetFat);
        }
    }
}
